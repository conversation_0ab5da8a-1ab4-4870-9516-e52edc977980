package com.yooa.system.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.constant.ServiceNameConstants;
import com.yooa.common.core.domain.R;
import com.yooa.system.api.domain.SysDept;
import com.yooa.system.api.domain.dto.QueryPage;
import com.yooa.system.api.domain.query.DeptQuery;
import com.yooa.system.api.domain.vo.GroupDeptVo;
import com.yooa.system.api.factory.RemoteDeptFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 *
 */
@FeignClient(contextId = "remoteDeptService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteDeptFallbackFactory.class)
public interface RemoteDeptService {

    String ClassPath = "dept";

    /**
     * 查询部门列表
     *
     * @param query  搜索条件
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping(ClassPath + "/getDeptList")
    R<List<SysDept>> getDeptList(@RequestBody DeptQuery query, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询分页部门列表
     *
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping(ClassPath + "/getPageDeptList")
    R<Page<SysDept>> getPageDeptList(@RequestBody QueryPage queryPage, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


}
