package com.yooa.crm.api.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 好友地区树形结构VO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class FriendAreaTreeVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 地区ID
     */
    private Long id;

    /**
     * 地区名称
     */
    private String area;

    /**
     * 父地区ID
     */
    private Long parentId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remark;

    /**
     * 子地区列表
     */
    private List<FriendAreaTreeVo> children;

    /**
     * 构造函数
     */
    public FriendAreaTreeVo() {
    }

    /**
     * 构造函数
     */
    public FriendAreaTreeVo(Long id, String area, Long parentId) {
        this.id = id;
        this.area = area;
        this.parentId = parentId;
    }
}
