package com.yooa.crm.api.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yooa.common.core.annotation.Excel;
import com.yooa.common.mybatis.base.BaseEntity;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 好友表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CrmFriend extends BaseEntity {

    /**
     * 好友ID
     */
    @TableId(value = "friend_id", type = IdType.AUTO)
    @Excel(name = "好友ID")
    private Long friendId;

    /**
     * 好友编码
     */
    @Excel(name = "好友编码")
    private String friendCode;

    /**
     * 姓名
     */
    @Excel(name = "好友名")
    private String friendName;

    /**
     * 联系方式
     */
    private Integer contactMode;

    /**
     * 联系号码
     */
    private String contactPhone;

    /**
     * 联系人url
     */
    private String contactsUrl;

    /**
     * 年龄
     */
    @Excel(name = "年龄")
    private Integer age;

    /**
     * 性别(字典)
     */
    @Excel(name = "性别", readConverterExp = "0=男,1=女,2=未知")
    private Integer sex;

    /**
     * 地区(字典)
     */
    @Excel(name = "地区", readConverterExp = "1=香港,2=台湾,3=新加坡,4=马来西亚,5=东南亚(非新加坡、马来),6=澳洲(澳大利亚新西兰),7=北美,8=南美,9=中东,10=欧洲,11=其他地区,12=日韩")
    private Integer area;

    /**
     * 婚姻状态(字典)
     */
    @Excel(name = "婚姻状态", readConverterExp = "1=未婚,2=已婚,3=其他")
    private Integer matrimony;

    /**
     * 情感需求(字典)
     */
    @Excel(name = "情感需求", readConverterExp = "1=恋爱,2=交友,3=结婚,4=猎奇,5=陪伴,6=娱乐消遣,7=其他")
    private Integer demand;

    /**
     * 工作类型(字典)
     */
    @Excel(name = "工作类型", readConverterExp = "1=白领(销售、管理、公务员等脑力型工作),2=蓝领(有固定月薪的技术型工作),3=劳务(有固定月薪的体力型工作)," +
            "4=服务业(固定月薪的服务型行业),5=短工(各种不稳定的临时工作),6=个体户(自己当老板),7=待业(失业),8=半工半读,9=学生,10=退休,11=自由职业(有稳定收入来源)")
    private Integer workType;

    /**
     * 语种(字典)
     */
    @Excel(name = "语种", readConverterExp = "1=汉语,2=英语")
    private Integer language;

    /**
     * 用户状态（0领取、1激活、2流失、3封存）
     */
    @Excel(name = "用户状态", readConverterExp = "0=领取,1=激活,2=流失,3=封存")
    private Integer status;

    /**
     * 粉丝类型（1男粉 2女粉 3英文粉 4中文粉）
     */
    @Excel(name = "粉丝类型", readConverterExp = "1=男粉,2=女粉,3=英文粉,4=中文粉")
    private String fansType;

    /**
     * 收入水平
     */
    private String incomeLevel;

    /**
     * 预计消费
     */
    private String projectedConsumption;

    /**
     * 主渠道ID
     */
    private Long mainChannelId;

    /**
     * 子渠道ID
     */
    private Long subChannelId;

    /**
     * 投手ID
     */
    @TableField(value = "pitcher_id", updateStrategy = FieldStrategy.IGNORED)
    private Long pitcherId;

    /**
     * 推广ID
     */
    private Long extendId;

    /**
     * 记录时间
     */
    private LocalDate recordDate;

    /**
     * 流失时间
     */
    private LocalDateTime loseTime;

    /**
     * 领取次数
     */
    private Integer receiveNumber;

    /**
     * 轮询类型1：有效,2:无效
     */
    private Long pollingType;

    /**
     * 轮询api客户id
     */
    private String pollingApiUserId;

    /**
     * 轮询api渠道（字典：crm_polling_channel_name）
     */
    private Integer pollingApiChannel;

    /**
     * 轮询api电话
     */
    private String pollingApiPhone;

    /**
     * 数据类型1系统好友，2轮询好友
     */
    private Long type;

    /**
     * 轮询填写的备注
     */
    private String pollingRemark;

    /**
     * 素材类型
     */
    private Long materialType;

    /**
     * 作息时间
     */
    private String restTime;

    /**
     * 娱乐方式
     */
    private String playWay;

    /**
     * 感兴趣话题
     */
    private String concernTownTalk;

    /**
     * 敏感话题
     */
    private String sensitiveTownTalk;

    /**
     * 感兴趣的app
     */
    private String concernApp;

    /**
     * 一级地区
     */
    private Integer mainAreaId;

    /**
     * 二级地区
     */
    private Integer subAreaId;

}