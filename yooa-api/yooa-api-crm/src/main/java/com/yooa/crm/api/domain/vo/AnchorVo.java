package com.yooa.crm.api.domain.vo;

import com.yooa.common.core.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> xh
 * @Date: 2025/4/7 16:12
 * @Description:
 */
@Data
public class AnchorVo {

    @Excel(name = "主播id")
    private Long anchorId;

    /**
     * pd账号
     */
    @Excel(name = "pd账号")
    private Long pdAnchorId;

    /**
     * 主播姓名
     */
    @Excel(name = "主播姓名")
    private String anchorName;

    @Excel(name = "pd主播名称")
    private String pdAnchorName;
    /**
     * 花名
     */
    @Excel(name = "花名")
    private String anchorNickName;

    @Excel(name = "运营id")
    private String operateId;

    /**
     * 性别（0男 1女）
     */
    @Excel(name = "性别", readConverterExp = "0=男,1=女")
    private Integer sex;

    /**
     * 语言（1中文 2英文）
     */
    @Excel(name = "语言", readConverterExp = "1=中文,2=英文")
    private Integer language;

    /**
     * 类别（1线上 2线下 3兼职）
     */
    @Excel(name = "类别", readConverterExp = "1=线上,2=线下,3=兼职")
    private Integer anchorType;

    /**
     * 开播权限 0=停播 1=正常
     */
    @Excel(name = "开播权限", readConverterExp = "0=停播,1=正常")
    private Integer liveRole;

    /**
     * 直播状态 (1开播中、2未开播、3已开播)
     */
    @Excel(name = "直播状态", readConverterExp = "1=开播中,2=未开播,3=已开播")
    private Integer liveStatus;

    /**
     * 状态 (0正常、1未开、2休播、3自离、4淘汰 5待停播)
     */
    @Excel(name = "主播状态", readConverterExp = "0=正常,1=未开,2=休播,3=自离,4=淘汰,5=待停播")
    private Integer userStatus;

//    /**
//     * 云账户
//     */
//    @Excel(name = "云账户")
//    private String cloudAccount;
//
//    /**
//     * 保底
//     */
//    @Excel(name = "保底")
//    private BigDecimal minimumAmt;

    /**
     * 运营部门id
     */
    // @Excel(friendName = "运营部门id")
    private Long deptId;

    /**
     * 运营部门
     */
    @Excel(name = "运营小组")
    private String deptName;

    /**
     * 运营父部门
     */
    @Excel(name = "运营父部门")
    private String ancestorsNames;

    /**
     * 运营
     */
    @Excel(name = "运营姓名")
    private String operationName;

    /**
     * 首充
     */
    @Excel(name = "首充")
    private Integer firstCharge;

    /**
     * 100粉
     */
    @Excel(name = "100粉")
    private Integer fans1h;

    /**
     * 200粉
     */
    @Excel(name = "200粉")
    private Integer fans2h;

    /**
     * 500粉
     */
//    @Excel(friendName = "500粉")
//    private Integer fans5h;

    /**
     * 5k粉
     */
    @Excel(name = "5K粉")
    private Integer fans5k;


    /**
     * 5w粉
     */
    @Excel(name = "5W粉")
    private Integer fans5w;

    /**
     * 10w粉
     */
    @Excel(name = "10W粉")
    private Integer fans10w;

    /**
     * 首充转换率
     */
    @Excel(name = "首充转换率")
    private Double firstChargeRate;

    /**
     * 交接粉丝
     */
    @Excel(name = "交接粉丝")
    private Integer joinFans;

    /**
     * 交接粉丝打赏率
     */
    @Excel(name = "交接粉丝打赏率")
    private Double joinFansRate;

    /**
     * 直播时长
     */
    @Excel(name = "直播时长")
    private Double liveHours;

    /**
     * 打赏
     */
    @Excel(name = "打赏")
    private BigDecimal reward;

    /**
     * 打赏率
     */
    @Excel(name = "打赏率")
    private BigDecimal rewardRate;

    /**
     * 总打赏
     */
    @Excel(name = "总打赏")
    private BigDecimal totalReward;

}
