package com.yooa.extend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.extend.api.domain.GoalExtendPack;
import com.yooa.extend.api.domain.dto.GoalExtendPackQueryDto;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 计划 - 数据层
 */
public interface GoalExtendPackMapper extends BaseMapper<GoalExtendPack> {

    /**
     * 查询计划打包列表
     */
    List<GoalExtendPack> selectGoalExtendPackList(Page<GoalExtendPack> page, @Param("query") GoalExtendPackQueryDto query);

    /**
     * 查询用户的最后一条计划
     *
     * @param query
     * @return
     */
    List<GoalExtendPack> selectPackList(@Param("query") GoalExtendPackQueryDto query);

    BigDecimal getYearPlan(@Param("deptId") Long deptId);

    BigDecimal getMonthPlan(@Param("deptId") Long deptId);
}




