package com.yooa.crm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.crm.api.domain.CrmFriendArea;
import com.yooa.crm.api.domain.vo.FriendAreaTreeVo;

import java.util.List;

/**
 * 好友地区 - 服务层
 */
public interface FriendAreaService extends IService<CrmFriendArea> {

    /**
     * 查询好友地区列表
     */
    List<CrmFriendArea> selectFriendAreaList(CrmFriendArea friendArea);

    /**
     * 根据ID查询好友地区
     */
    CrmFriendArea selectFriendAreaById(Long id);

    /**
     * 新增好友地区
     */
    int insertFriendArea(CrmFriendArea friendArea);

    /**
     * 修改好友地区
     */
    int updateFriendArea(CrmFriendArea friendArea);

    /**
     * 批量删除好友地区
     */
    int deleteFriendAreaByIds(Long[] ids);

    /**
     * 删除好友地区信息
     */
    int deleteFriendAreaById(Long id);

    /**
     * 构建前端所需要的树结构
     */
    List<FriendAreaTreeVo> buildFriendAreaTree(List<CrmFriendArea> friendAreas);

    /**
     * 查询好友地区树结构信息
     */
    List<FriendAreaTreeVo> selectFriendAreaTreeList(CrmFriendArea friendArea);

    /**
     * 校验地区名称是否唯一
     */
    boolean checkAreaNameUnique(CrmFriendArea friendArea);

    /**
     * 是否存在地区子节点
     */
    boolean hasChildByAreaId(Long areaId);
}
