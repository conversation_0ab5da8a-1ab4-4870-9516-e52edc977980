package com.yooa.crm.controller;

import cn.hutool.core.date.ChineseDate;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.utils.poi.ExcelUtil;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.common.security.utils.ExcelConvertUtil;
import com.yooa.crm.api.constant.AnchorInfoConstants;
import com.yooa.crm.api.domain.CrmAnchorAccountMapping;
import com.yooa.crm.api.domain.CrmAnchorInfo;
import com.yooa.crm.api.domain.dto.AnchorInfoEditDto;
import com.yooa.crm.api.domain.dto.AnchorInfoEditStatusDto;
import com.yooa.crm.api.domain.dto.AnchorInfoSaveDto;
import com.yooa.crm.api.domain.query.AnchorInfoQuery;
import com.yooa.crm.api.domain.query.AnchorRosterQuery;
import com.yooa.crm.api.domain.vo.AnchorAccountVo;
import com.yooa.crm.api.domain.vo.AnchorInfoVo;
import com.yooa.crm.api.domain.vo.AnchorRosterVo;
import com.yooa.crm.enums.AnchorUserStatus;
import com.yooa.crm.mapper.CrmAnchorInfoMapper;
import com.yooa.crm.mapper.CrmAnchorOperateMapper;
import com.yooa.crm.service.AnchorAccountMappingService;
import com.yooa.crm.service.AnchorInfoService;
import com.yooa.crm.service.AnchorService;
import com.yooa.crm.service.AnchorStyleService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;

/**
 * 主播信息 - 控制层
 */
@Validated
@AllArgsConstructor
@RestController
@RequestMapping("/anchor-info")
public class AnchorInfoController extends BaseController {

    private final AnchorService anchorService;
    private final AnchorInfoService anchorInfoService;
    private final AnchorAccountMappingService anchorAccountMappingService;
    private final CrmAnchorOperateMapper anchorOperateMapper;
    private final CrmAnchorInfoMapper crmAnchorInfoMapper;
    private final AnchorStyleService anchorStyleService;

    /**
     * 获取主播招募列表 - 运营板块
     */
    @GetMapping("/list")
    public AjaxResult list(Page<AnchorInfoVo> page, @Valid AnchorInfoQuery query) {
        return success(page.setRecords(anchorInfoService.list(page, query)));
    }


    /**
     * 获取主播详情
     */
    @GetMapping("/{anchorId}")
    public AjaxResult info(@PathVariable Long anchorId) {
        return success(anchorInfoService.getAnchorInfoById(anchorId));
    }

    /**
     * 导出主播信息
     */
    @PostMapping("/export")
    public void export(HttpServletResponse response, AnchorInfoQuery query) {
        List<AnchorInfoVo> anchorInfoList = anchorInfoService.list(null, query);
        ExcelUtil<AnchorInfoVo> util = new ExcelUtil<>(AnchorInfoVo.class);
        util.exportExcel(response, anchorInfoList, "主播信息列表");
    }

    /**
     * 新增主播信息
     */
    @PostMapping
    public AjaxResult save(@Valid @RequestBody AnchorInfoSaveDto anchorInfo) {
        anchorInfo.setInterviewDate(LocalDate.now());
        anchorInfo.setChineseBirthdayDate(new ChineseDate(anchorInfo.getBirthdayDate()).toString());
        anchorInfo.setAnchorStatus("0");
        return success(anchorInfoService.save(anchorInfo));
    }

    /**
     * 修改主播信息
     */
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody AnchorInfoEditDto anchorInfo) {
        return success(anchorInfoService.updateById(anchorInfo));
    }

    /**
     * 修改主播状态
     */
    @PutMapping("/status")
    public AjaxResult editStatus(@Valid @RequestBody AnchorInfoEditStatusDto anchorInfoEditStatusDto) {
        anchorInfoService.update(
                Wrappers.<CrmAnchorInfo>lambdaUpdate()
                        .set(CrmAnchorInfo::getAnchorStatus, anchorInfoEditStatusDto.getAnchorStatus())
                        .set(ObjUtil.isNotNull(anchorInfoEditStatusDto.getFirstRecruiterId()), CrmAnchorInfo::getFirstRecruiterId, anchorInfoEditStatusDto.getFirstRecruiterId())
                        .set(ObjUtil.isNotNull(anchorInfoEditStatusDto.getFinalRecruiterId()), CrmAnchorInfo::getFinalRecruiterId, anchorInfoEditStatusDto.getFinalRecruiterId())
                        .set(ObjUtil.isNotNull(anchorInfoEditStatusDto.getCooperationDate()), CrmAnchorInfo::getCooperationDate, anchorInfoEditStatusDto.getCooperationDate())
                        .eq(CrmAnchorInfo::getAnchorId, anchorInfoEditStatusDto.getAnchorId())
        );
        return success();
    }

    /**
     * 修改主播运营
     */
    @PutMapping("/operate/{anchorId}/{operateId}")
    public AjaxResult editOperate(@PathVariable Long anchorId, @PathVariable Long operateId) {
        anchorInfoService.update(
                Wrappers.<CrmAnchorInfo>lambdaUpdate()
                        .set(CrmAnchorInfo::getOperateId, operateId)
                        .eq(CrmAnchorInfo::getAnchorId, anchorId)
        );
        return success();
    }

    /**
     * 修改主播风格信息
     */
    @PutMapping("/style/{anchorId}")
    public AjaxResult editStyle(@PathVariable Long anchorId, @RequestBody CrmAnchorInfo anchorInfo) {
        anchorInfoService.update(
                Wrappers.<CrmAnchorInfo>lambdaUpdate()
                        .set(CrmAnchorInfo::getAnchorStyle, anchorInfo.getAnchorStyle())
                        .set(CrmAnchorInfo::getAnchorType, anchorInfo.getAnchorType())
                        .set(CrmAnchorInfo::getLanguage, anchorInfo.getLanguage())
                        .eq(CrmAnchorInfo::getAnchorId, anchorId)
        );
        return success();
    }

    /**
     * 修改主播运营
     */
    @PutMapping("/start-live-date/{anchorId}")
    public AjaxResult editOperate(@PathVariable Long anchorId, LocalDate startLiveDate) {
        anchorInfoService.update(
                Wrappers.<CrmAnchorInfo>lambdaUpdate()
                        .set(CrmAnchorInfo::getStartLiveDate, startLiveDate)
                        .eq(CrmAnchorInfo::getAnchorId, anchorId)
        );
        return success();
    }

    /**
     * 获取主播绑定的账号列表
     */
    @GetMapping("/account-list/{anchorId}")
    public AjaxResult accountList(@PathVariable Long anchorId) {
        return success(anchorInfoService.accountListByAnchorId(anchorId));
    }

    /**
     * 获取输入的主播账号id信息
     */
    @GetMapping("/account-info/{accountId}")
    public AjaxResult accountInfo(@PathVariable Long accountId) {

        AnchorAccountVo anchorAccount = anchorService.getByAccountId(accountId);
        if (ObjUtil.isEmpty(anchorAccount)) {
            return warn("[" + accountId + "] 不存在该主播账号");
        }

        CrmAnchorAccountMapping anchorAccountMapping = anchorAccountMappingService.getOne(
                Wrappers.<CrmAnchorAccountMapping>lambdaQuery()
                        .eq(CrmAnchorAccountMapping::getAccountId, accountId)
        );

        if (ObjUtil.isNotNull(anchorAccountMapping)) {
            CrmAnchorInfo anchorInfo = anchorInfoService.getById(anchorAccountMapping.getAnchorId());
            if (anchorInfo != null) {
                return warn("[" + accountId + "]已被主播“" + anchorInfo.getAnchorName() + "”绑定");
            }
            return warn(StrUtil.format("未查询到主播id为:[{}]的账号", accountId));
        }

        return success(anchorAccount);
    }

    /**
     * 绑定主播账号id
     */
    @PostMapping("/bind/{anchorId}/{accountId}")
    public AjaxResult bindAccountId(@PathVariable Long anchorId, @PathVariable Long accountId) {
        CrmAnchorAccountMapping anchorAccountMapping = anchorAccountMappingService.getOne(
                Wrappers.<CrmAnchorAccountMapping>lambdaQuery()
                        .eq(CrmAnchorAccountMapping::getAccountId, accountId)
        );
        CrmAnchorInfo anchorInfo = anchorInfoService.getById(anchorId);

        Long operateId = 0L;

        if (anchorInfo != null) {
            operateId = anchorInfo.getOperateId();
        }

        // 查询用户和PY绑定表(sys_user_pd)中 是否绑定了PY的运营ID
        int existsBind = crmAnchorInfoMapper.existsBindPyOperateId(operateId);
        if (existsBind == 0) {
            return warn("请先绑定py运营id");
        }

        if (ObjUtil.isNotNull(anchorAccountMapping)) {
            CrmAnchorInfo crmAnchorInfo = anchorInfoService.getById(anchorAccountMapping.getAnchorId());
            return warn("[" + accountId + "]已被主播“" + crmAnchorInfo.getAnchorName() + "”绑定");
        }

        // 查询主播运营绑定表中 该主播账号在PD绑定的运营ID和当前去绑定该主播的运营ID是否一致
        int count = anchorOperateMapper.existsByAnchorIdAndUserId(accountId, operateId);
        if (count == 0) {
            return warn(StrUtil.format("主播id绑定有误 当前主播id[{}]在PD绑定的运营id与当前运营id不一致", accountId));
        }


        anchorAccountMapping = new CrmAnchorAccountMapping();
        anchorAccountMapping.setAnchorId(anchorId);
        anchorAccountMapping.setAccountId(accountId);

        return success(anchorAccountMappingService.save(anchorAccountMapping));
    }

    /**
     * 取消绑定主播账号id
     */
    @DeleteMapping("/unbind/{anchorId}/{accountId}")
    public AjaxResult unbindAccountId(@PathVariable Long anchorId, @PathVariable Long accountId) {
        anchorAccountMappingService.remove(
                Wrappers.<CrmAnchorAccountMapping>lambdaQuery()
                        .eq(CrmAnchorAccountMapping::getAnchorId, anchorId)
                        .eq(CrmAnchorAccountMapping::getAccountId, accountId)
        );
        return success();
    }

    /**
     * 获取主播的运营列表
     * type: 1-当前 2-历史
     */
    @GetMapping("/operate-list/{anchorId}/{type}")
    public AjaxResult operateListByAnchorId(@PathVariable Long anchorId, @PathVariable Integer type) {
        return success(anchorAccountMappingService.operateListByAnchorId(anchorId, type));
    }

    /**
     * 主播账号信息详情
     */
    @GetMapping("/getAnchorInfoDetail/{anchorId}")
    public AjaxResult getAnchorInfoDetail(@PathVariable Long anchorId) {
        return success(anchorAccountMappingService.getAnchorInfoDetail(anchorId));
    }

    /**
     * 主播花名册
     */
    @GetMapping("/getAnchorRoster")
    public AjaxResult getAnchorRoster(Page page, AnchorRosterQuery query) {
        return success(page.setRecords(anchorInfoService.getAnchorRoster(page, query)));
    }

    /**
     * 主播花名册(导出)
     */
    @PostMapping("/getAnchorRoster/export")
    public void getAnchorRoster(HttpServletResponse response, Page page, AnchorRosterQuery query) {
        page.setSize(-1);
        List<AnchorRosterVo> anchorInfoList = anchorInfoService.getAnchorRoster(page, query);
        anchorInfoList.forEach(anchorManageVo -> {
            anchorStyleService.getOptById(anchorManageVo.getAnchorStyle()).ifPresent(anchorStyle -> anchorManageVo.setAnchorStyle(anchorStyle.getStyleName()));
        });
        ExcelConvertUtil.process(anchorInfoList);
        ExcelUtil<AnchorRosterVo> util = new ExcelUtil<>(AnchorRosterVo.class);
        util.exportExcel(response, anchorInfoList, "主播花名册");
    }

    /**
     * 登记表URL
     */
    @GetMapping("/registration-form-url/{anchorId}")
    public AjaxResult getRegistrationFormUrl(@PathVariable Long anchorId) {
        return success(anchorInfoService.getRegistrationFormUrl(anchorId));
    }


}
