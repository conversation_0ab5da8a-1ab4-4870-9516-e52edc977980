package com.yooa.crm.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.crm.api.domain.CrmCustomer;
import com.yooa.crm.api.domain.dto.ExtendDataDto;
import com.yooa.crm.api.domain.query.CustomerDataTotalPerformanceQuery;
import com.yooa.crm.api.domain.query.CustomerQuery;
import com.yooa.crm.api.domain.vo.CustomerDataTotalPerformanceVo;
import com.yooa.crm.api.domain.vo.CustomerVo;
import com.yooa.crm.api.domain.vo.FriendCustomerDetailsVo;
import com.yooa.extend.api.domain.vo.ExtendDataDeptGroupVo;
import com.yooa.extend.api.domain.vo.ExtendDataTimeGroupVo;
import com.yooa.extend.api.domain.vo.ExtendDataUserGroupVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户 - 数据层
 */
public interface CrmCustomerMapper extends BaseMapper<CrmCustomer> {

    /**
     * 获取PD客户列表(推广)
     */
    List<CrmCustomer> selectCustomerList(Page<CrmCustomer> page, @Param("query") CustomerQuery query);

    /**
     * 获取PD客户列表(vip板块)
     */
    List<CrmCustomer> selectCustomerVipList(Page<CrmCustomer> page, @Param("query") CustomerQuery query);

    FriendCustomerDetailsVo selFriendCustomerVo(@Param("id") Long id);

    List<CustomerVo> selCustomerVoList(@Param("id") Long id);

    // 刷优质用户
    int updateQualityStatus();

    List<CrmCustomer> getCustomerNameByCustomerIds(@Param("ids") List<Long> customerIds);

    List<Long> getCustomerIdsByName(@Param("queryId") String queryId);

    /**
     * 数据统计 (客户数据)
     */
    List<CustomerDataTotalPerformanceVo> getCustomerDataTotalPerformance(Page page, @Param("query") CustomerDataTotalPerformanceQuery query);


    /*******推广数据统计*******/

    /**
     * 根据用户分组总业绩
     *
     * @return
     */
    List<ExtendDataUserGroupVo> orderUserGroup(@Param("dto") ExtendDataDto dto);


    /**
     * 根据用户分组新增业绩
     *
     * @return
     */
    List<ExtendDataUserGroupVo> addOrderUserGroup(@Param("dto") ExtendDataDto dto);

    /**
     * 根据用户分组好友
     *
     * @return
     */
    List<ExtendDataUserGroupVo> friendUserGroup(@Param("dto") ExtendDataDto dto);

    /**
     * 根据用户分组注册
     *
     * @return
     */
    List<ExtendDataUserGroupVo> registerUserGroup(@Param("dto") ExtendDataDto dto);

    /**
     * 根据用户分组优质
     *
     * @return
     */
    List<ExtendDataUserGroupVo> qualityUsersUserGroup(@Param("dto") ExtendDataDto dto);

    /**
     * 根据用户分组交接(type = 1:一交、2:二交)
     *
     * @return
     */
    List<ExtendDataUserGroupVo> handoverUserGroup(@Param("dto") ExtendDataDto dto, @Param("type") Integer type);

    /**
     * 根据用户分组粉丝登记(type = 0:200粉、1:500粉、2:5k粉、3:5w粉、5:首充粉、6:10w粉、7:40w粉、8:100w粉)
     *
     * @return
     */
    List<ExtendDataUserGroupVo> extendVermicelliUserGroup(@Param("dto") ExtendDataDto dto, @Param("type") Integer type);

    /**
     * 根据部门分组总业绩
     *
     * @return
     */
    List<ExtendDataDeptGroupVo> orderDeptGroup(@Param("dto") ExtendDataDto dto);


    /**
     * 根据部门分组新增业绩
     *
     * @return
     */
    List<ExtendDataDeptGroupVo> addOrderDeptGroup(@Param("dto") ExtendDataDto dto);

    /**
     * 根据部门分组好友
     *
     * @return
     */
    List<ExtendDataDeptGroupVo> friendDeptGroup(@Param("dto") ExtendDataDto dto);

    /**
     * 根据部门分组注册
     *
     * @return
     */
    List<ExtendDataDeptGroupVo> registerDeptGroup(@Param("dto") ExtendDataDto dto);

    /**
     * 根据部门分组优质
     *
     * @return
     */
    List<ExtendDataDeptGroupVo> qualityUsersDeptGroup(@Param("dto") ExtendDataDto dto);

    /**
     * 根据部门分组交接(type = 1:一交、2:二交)
     *
     * @return
     */
    List<ExtendDataDeptGroupVo> handoverDeptGroup(@Param("dto") ExtendDataDto dto, @Param("type") Integer type);

    /**
     * 根据部门分组粉丝登记(type = 0:200粉、1:500粉、2:5k粉、3:5w粉、5:首充粉、6:10w粉、7:40w粉、8:100w粉)
     *
     * @return
     */
    List<ExtendDataDeptGroupVo> extendVermicelliDeptGroup(@Param("dto") ExtendDataDto dto, @Param("type") Integer type);

    /**
     * 根据时间分组总业绩
     *
     * @return
     */
    List<ExtendDataTimeGroupVo> orderTimeGroup(@Param("dto") ExtendDataDto dto);

    /**
     * 根据时间分组新增业绩
     *
     * @return
     */
    List<ExtendDataTimeGroupVo> addOrderTimeGroup(@Param("dto") ExtendDataDto dto);

    /**
     * 根据时间分组好友
     *
     * @return
     */
    List<ExtendDataTimeGroupVo> friendTimeGroup(@Param("dto") ExtendDataDto dto);

    /**
     * 根据时间分组注册
     *
     * @return
     */
    List<ExtendDataTimeGroupVo> registerTimeGroup(@Param("dto") ExtendDataDto dto);

    /**
     * 根据时间分组优质
     *
     * @return
     */
    List<ExtendDataTimeGroupVo> qualityUsersTimeGroup(@Param("dto") ExtendDataDto dto);

    /**
     * 根据时间分组交接(type = 1:一交、2:二交)
     *
     * @return
     */
    List<ExtendDataTimeGroupVo> handoverTimeGroup(@Param("dto") ExtendDataDto dto, @Param("type") Integer type);

    /**
     * 根据时间分组粉丝登记(type = 0:200粉、1:500粉、2:5k粉、3:5w粉、5:首充粉、6:10w粉、7:40w粉、8:100w粉)
     *
     * @return
     */
    List<ExtendDataTimeGroupVo> extendVermicelliTimeGroup(@Param("dto") ExtendDataDto dto, @Param("type") Integer type);

    /**
     * 根据时间分组月计划
     *
     * @return
     */
    List<ExtendDataTimeGroupVo> goalTimeGroup(@Param("dto") ExtendDataDto dto);
}




