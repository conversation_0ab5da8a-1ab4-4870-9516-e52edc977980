package com.yooa.crm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.crm.api.domain.CrmFriendArea;
import com.yooa.crm.api.domain.vo.FriendAreaTreeVo;
import com.yooa.crm.mapper.CrmFriendAreaMapper;
import com.yooa.crm.service.FriendAreaService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 好友地区 - 服务实现层
 */
@Service
@AllArgsConstructor
public class FriendAreaServiceImpl extends ServiceImpl<CrmFriendAreaMapper, CrmFriendArea> implements FriendAreaService {

    @Override
    public List<CrmFriendArea> selectFriendAreaList(CrmFriendArea friendArea) {
        LambdaQueryWrapper<CrmFriendArea> queryWrapper = new LambdaQueryWrapper<>();
        
        if (ObjUtil.isNotNull(friendArea.getParentId())) {
            queryWrapper.eq(CrmFriendArea::getParentId, friendArea.getParentId());
        }
        if (StrUtil.isNotBlank(friendArea.getArea())) {
            queryWrapper.like(CrmFriendArea::getArea, friendArea.getArea());
        }
        
        queryWrapper.orderByAsc(CrmFriendArea::getSort);
        return list(queryWrapper);
    }

    @Override
    public CrmFriendArea selectFriendAreaById(Long id) {
        return getById(id);
    }

    @Override
    public int insertFriendArea(CrmFriendArea friendArea) {
        return save(friendArea) ? 1 : 0;
    }

    @Override
    public int updateFriendArea(CrmFriendArea friendArea) {
        return updateById(friendArea) ? 1 : 0;
    }

    @Override
    public int deleteFriendAreaByIds(Long[] ids) {
        return removeByIds(CollUtil.toList(ids)) ? ids.length : 0;
    }

    @Override
    public int deleteFriendAreaById(Long id) {
        return removeById(id) ? 1 : 0;
    }

    @Override
    public List<FriendAreaTreeVo> buildFriendAreaTree(List<CrmFriendArea> friendAreas) {
        List<FriendAreaTreeVo> returnList = new ArrayList<>();
        List<Long> tempList = friendAreas.stream().map(CrmFriendArea::getId).collect(Collectors.toList());
        
        for (Iterator<CrmFriendArea> iterator = friendAreas.iterator(); iterator.hasNext(); ) {
            CrmFriendArea area = iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(area.getParentId())) {
                FriendAreaTreeVo treeVo = convertToTreeVo(area);
                recursionFn(friendAreas, treeVo);
                returnList.add(treeVo);
            }
        }
        
        if (returnList.isEmpty()) {
            returnList = friendAreas.stream().map(this::convertToTreeVo).collect(Collectors.toList());
        }
        
        return returnList;
    }

    @Override
    public List<FriendAreaTreeVo> selectFriendAreaTreeList(CrmFriendArea friendArea) {
        List<CrmFriendArea> friendAreas = selectFriendAreaList(friendArea);
        return buildFriendAreaTree(friendAreas);
    }

    @Override
    public boolean checkAreaNameUnique(CrmFriendArea friendArea) {
        Long areaId = ObjUtil.isNull(friendArea.getId()) ? -1L : friendArea.getId();
        CrmFriendArea info = getOne(new LambdaQueryWrapper<CrmFriendArea>()
                .eq(CrmFriendArea::getArea, friendArea.getArea())
                .eq(CrmFriendArea::getParentId, friendArea.getParentId())
                .last("limit 1"));
        
        if (ObjUtil.isNotNull(info) && !info.getId().equals(areaId)) {
            return false;
        }
        return true;
    }

    @Override
    public boolean hasChildByAreaId(Long areaId) {
        long count = count(new LambdaQueryWrapper<CrmFriendArea>()
                .eq(CrmFriendArea::getParentId, areaId));
        return count > 0;
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<CrmFriendArea> list, FriendAreaTreeVo t) {
        // 得到子节点列表
        List<FriendAreaTreeVo> childList = getChildList(list, t);
        t.setChildren(childList);
        for (FriendAreaTreeVo tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<FriendAreaTreeVo> getChildList(List<CrmFriendArea> list, FriendAreaTreeVo t) {
        List<FriendAreaTreeVo> tlist = new ArrayList<>();
        Iterator<CrmFriendArea> it = list.iterator();
        while (it.hasNext()) {
            CrmFriendArea n = it.next();
            if (ObjUtil.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getId().longValue()) {
                tlist.add(convertToTreeVo(n));
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<CrmFriendArea> list, FriendAreaTreeVo t) {
        return !getChildList(list, t).isEmpty();
    }

    /**
     * 转换为树形VO
     */
    private FriendAreaTreeVo convertToTreeVo(CrmFriendArea friendArea) {
        FriendAreaTreeVo treeVo = new FriendAreaTreeVo();
        treeVo.setId(friendArea.getId());
        treeVo.setArea(friendArea.getArea());
        treeVo.setParentId(friendArea.getParentId());
        treeVo.setSort(friendArea.getSort());
        treeVo.setRemark(friendArea.getRemark());
        return treeVo;
    }
}
