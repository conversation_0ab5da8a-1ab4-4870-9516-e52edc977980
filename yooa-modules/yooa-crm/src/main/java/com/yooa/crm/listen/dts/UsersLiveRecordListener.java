package com.yooa.crm.listen.dts;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.ImmutableList;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.yooa.common.core.constant.KafkaTopicConstants;
import com.yooa.common.core.domain.SubscribeConvertDbData;
import com.yooa.common.core.utils.LocalDateUtil;
import com.yooa.crm.api.domain.CrmAnchor;
import com.yooa.crm.api.domain.CrmAnchorAccountMapping;
import com.yooa.crm.api.domain.CrmAnchorInfo;
import com.yooa.crm.api.domain.CrmAnchorLiveRecord;
import com.yooa.crm.api.domain.dto.AnchorLiveTimeDto;
import com.yooa.crm.mapper.CrmAnchorInfoMapper;
import com.yooa.crm.service.AnchorAccountMappingService;
import com.yooa.crm.service.AnchorInfoService;
import com.yooa.crm.service.AnchorLiveRecordService;
import com.yooa.crm.service.AnchorService;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static com.yooa.common.core.utils.ObjectConvertUtil.convertTo;
import static com.yooa.common.core.utils.ObjectConvertUtil.convertToLong;

/**
 *
 */
@Service
@RequiredArgsConstructor
public class UsersLiveRecordListener extends AbstractBaseListener {

    private final AnchorLiveRecordService anchorLiveRecordService;

    private final AnchorService anchorService;

    private final AnchorAccountMappingService anchorAccountMappingService;

    private final CrmAnchorInfoMapper anchorInfoMapper;

    @Override
    public String getTopic() {
        return KafkaTopicConstants.CMF_USERS_LIVERECORD;
    }

//    @Override
//    public String getBusTopic() {
//        return KafkaBusinessTopicConstants.BUS_CMF_USERS_LIVERECORD;
//    }

    @Override
    public void dltBusiness(ConsumerRecord<String, String> consumerRecord, Acknowledgment acknowledgment) {
        acknowledgment.acknowledge();

    }

    @Override
    protected List<String> getFilterList() {
        return ImmutableList.of("id", "app_project", "uid", "showid", "starttime", "endtime", "votes", "liveclassid");
    }

    @Override
    protected void handleInsert(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {
        saveAnchorLiveRecord(fieldMap);
    }

    @Override
    protected void handleUpdate(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {

    }

    @Override
    protected void handleDelete(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {

    }

    protected void saveAnchorLiveRecord(Map<String, SubscribeConvertDbData.Field> fieldMap) {
        String id = getAfterValueByFieldName("id", fieldMap);
        // 平台 1:poyo 2:潘多拉 3:助手
        String appProject = getAfterValueByFieldName("app_project", fieldMap);
        // 主播ID
        String anchorId = getAfterValueByFieldName("uid", fieldMap);
        // 直播标识
        String showid = getAfterValueByFieldName("showid", fieldMap);
        // 开始时间
        String startTime = getAfterValueByFieldName("starttime", fieldMap);
        LocalDateTime startDateTime = StrUtil.isNotBlank(startTime) ? LocalDateUtil.epochSecondToLocalDateTime(startTime) : null;
        // 结束时间
        String endTime = getAfterValueByFieldName("endtime", fieldMap);
        LocalDateTime endDateTime = StrUtil.isNotBlank(endTime) ? LocalDateUtil.epochSecondToLocalDateTime(endTime) : null;
        // 本次直播收益
        String votes = getAfterValueByFieldName("votes", fieldMap);
        // 直播分类ID
        String liveClassId = getAfterValueByFieldName("liveclassid", fieldMap);
        // 构建入参实体
        CrmAnchorLiveRecord crmAnchorLiveRecord = buildCrmAnchorLiveRecord(id, appProject, anchorId, showid, startDateTime, endDateTime, votes, liveClassId);
        // 入库
        anchorLiveRecordService.save(crmAnchorLiveRecord);
        // 设置主播直播时长
        updateAnchorLiveTime(convertToLong(anchorId));
        super.sendBusTopic = true;
    }

    private CrmAnchorLiveRecord buildCrmAnchorLiveRecord(String id, String appProject, String anchorId, String showid, LocalDateTime startTime,
            LocalDateTime endTime, String votes, String liveClassId) {
        return CrmAnchorLiveRecord.builder()
                .id(convertToLong(id))
                .appProject(convertTo(appProject, Integer.class))
                .anchorId(convertToLong(anchorId))
                .showId(convertTo(showid, Integer.class))
                .startTime(startTime)
                .endTime(endTime)
                .votes(StrUtil.isNotBlank(votes) ? NumberUtil.toBigDecimal(votes) : null)
                .liveClassId(convertTo(liveClassId, Integer.class))
                .build();

    }

    /**
     * 更新主播直播时间
     *
     * @param anchorId 主播id
     */
    private void updateAnchorLiveTime(Long anchorId) {
        // 查询出该主播当日直播时长 当月直播时长  总直播时长汇总
        AnchorLiveTimeDto anchorLiveTimeDto = anchorLiveRecordService.getAnchorLiveListByCustomerId(anchorId);

        // 设置主播直播时长
        CrmAnchor crmAnchor = new CrmAnchor();
        crmAnchor.setAnchorId(anchorId);
        crmAnchor.setTodayLiveDate(LocalDateTime.now());
        crmAnchor.setTodayLiveHour(anchorLiveTimeDto.getTodayHours());
        crmAnchor.setMonthLiveHour(anchorLiveTimeDto.getMonthHours());
        crmAnchor.setTotalLiveHour(anchorLiveTimeDto.getTotalHours());

        // 主播总直播时长大于1小时 更改主播状态为开播中
/*        if(StrUtil.isNotBlank(anchorLiveTimeDto.getTotalHours())){
            BigDecimal bigDecimal = Convert.toBigDecimal(anchorLiveTimeDto.getTotalHours());
            if (bigDecimal.compareTo(BigDecimal.ONE) > 0) {
                CrmAnchorAccountMapping crmAnchorAccountMapping = anchorAccountMappingService.getOne(new LambdaQueryWrapper<CrmAnchorAccountMapping>()
                        .eq(CrmAnchorAccountMapping::getAccountId, anchorId)
                        .last("litmi 1"));
                if(crmAnchorAccountMapping != null){
                    new LambdaUpdateChainWrapper<>(anchorInfoMapper)
                            .eq(CrmAnchorInfo::getAnchorId,crmAnchorAccountMapping.getAnchorId())
                            .set(CrmAnchorInfo::getAnchorStatus,"6")
                            .update(new CrmAnchorInfo());
                }
            }
        }*/


        anchorService.updateById(crmAnchor);

    }

}
