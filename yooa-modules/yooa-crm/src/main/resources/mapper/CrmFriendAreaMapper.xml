<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.crm.mapper.CrmFriendAreaMapper">

    <resultMap type="com.yooa.crm.api.domain.CrmFriendArea" id="CrmFriendAreaResult">
        <result property="id" column="id"/>
        <result property="area" column="area"/>
        <result property="parentId" column="parent_id"/>
        <result property="sort" column="sort"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

</mapper>
