<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.crm.mapper.CrmCustomerMapper">

    <resultMap id="CustomerDataTotalPerformanceResult" type="com.yooa.crm.api.domain.vo.CustomerDataTotalPerformanceVo">
        <id property="customerId" column="customerId"/>
        <result property="customerName" column="customerName"/>
        <result property="customerNickName" column="customerNickName"/>
        <result property="monthRechargeAmount" column="monthOrderMoney"/>
        <result property="totalRechargeAmount" column="sumOrderMoney" />
        <result property="extendName" column="extendName" />
        <result property="extendDeptName" column="extendDeptName" />
        <result property="extendAncestors" column="extendAncestorsNames"/>
        <result property="serveName" column="serveName"/>
        <result property="serveDeptName" column="serveDeptName" />
        <result property="serveAncestors" column="serveAncestorsNames"/>
        <collection property="anchorOperate" ofType="com.yooa.crm.api.domain.vo.AnchorOperateVo" column="{customerId = customerId}"
                    select="selectAnchorOperateList"/>
    </resultMap>

    <select id="selectAnchorOperateList" resultType="com.yooa.crm.api.domain.vo.AnchorOperateVo">
        SELECT
            ao.operate_id,
            a.anchor_id,
            a.anchor_name,
            u.user_id,
            u.user_name,
            u.nick_name,
            d.dept_name,
            d.ancestors_names
        FROM
            crm_customer_join_anchor cja
                LEFT JOIN crm_anchor a ON a.anchor_id = cja.anchor_id
                LEFT JOIN crm_anchor_operate ao ON ao.anchor_id = cja.anchor_id
                LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cja.operate_id
                LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
                LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        WHERE
            cja.status = 1
          AND cja.customer_id = #{customerId}
    </select>

    <select id="selectCustomerList" resultType="com.yooa.crm.api.domain.CrmCustomer">
        SELECT
            c.customer_id,
            c.customer_name,
            c.customer_account,
            (CASE WHEN f.friend_id IS NOT NULL THEN '0' ELSE '1' END) AS status,
            c.reg_ip,
            c.region,
            c.source,
            c.uuid_device,
            c.extend_id,
            c.last_login_time,
            c.create_time,
            c.update_time
        FROM yooa_system.sys_user u
        LEFT JOIN yooa_system.sys_user_pd up ON up.user_id = u.user_id
        LEFT JOIN yooa_crm.crm_customer c ON c.extend_id = up.pd_user_id
        LEFT JOIN yooa_crm.crm_customer_friend AS f ON c.customer_id = f.customer_id AND c.extend_id = f.py_extend_id AND f.status = 0
        WHERE
            c.extend_id IS NOT NULL
            AND u.user_id = #{query.userId}
            <if test="query.customerId != null">
                AND c.customer_id like concat('%', #{query.customerId}, '%')
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND c.customer_name like concat('%', #{query.customerName}, '%')
            </if>
            <if test="query.customerAccount != null and query.customerAccount != ''">
                AND c.customer_account like concat('%', #{query.customerAccount}, '%')
            </if>
            <if test="query.customerId != null">
                AND c.customer_id like concat('%', #{query.customerId}, '%')
            </if>
            <if test="query.source != null and query.source != ''">
                AND c.source = #{query.source}
            </if>
            <if test="query.regIp != null and query.regIp != ''">
                AND c.reg_ip LIKE concat('%', #{query.regIp}, '%')
            </if>
            <if test="query.createDate != null">
                AND (DATE(c.create_time) BETWEEN #{query.createDate[0]} AND #{query.createDate[1]})
            </if>
            <if test="query.updateDate != null">
                AND (DATE(c.update_time) BETWEEN #{query.updateDate[0]} AND #{query.updateDate[1]})
            </if>
            <if test="query.status != null">
                <if test="query.status == 0">
                    AND f.friend_id IS NOT NULL
                </if>
                <if test="query.status == 1">
                    AND f.friend_id IS NULL
                </if>
            </if>
        GROUP BY c.customer_id
    </select>

    <select id="selectCustomerVipList" resultType="com.yooa.crm.api.domain.CrmCustomer">
        SELECT data.* FROM (
            SELECT
                c.customer_id,
                c.customer_name,
                c.customer_account,
                (CASE WHEN f.friend_id IS NOT NULL THEN '0' ELSE '1' END) AS status,
                c.reg_ip,
                c.region,
                c.source,
                c.uuid_device,
                c.extend_id,
                c.last_login_time,
                c.create_time,
                c.update_time,
                c.serve_id,
                ROW_NUMBER() OVER ( PARTITION BY c.customer_id ORDER BY s.receive_time DESC ) AS rn
            FROM yooa_system.sys_user u
            LEFT JOIN yooa_system.sys_user_pd up ON up.user_id = u.user_id
            LEFT JOIN yooa_crm.crm_customer c ON c.serve_id = up.pd_user_id
            LEFT JOIN yooa_crm.crm_customer_join_serve s ON c.customer_id = s.customer_id AND s.serve_id = c.serve_id AND s.`status` = 1
            LEFT JOIN yooa_crm.crm_customer_friend AS f ON s.customer_id = f.customer_id AND f.py_extend_id = s.extend_id
            WHERE c.extend_id IS NOT NULL
            AND f.friend_id IS NULL
            AND u.user_id = #{query.userId}
            <if test="query.customerId != null">
                AND c.customer_id like concat('%', #{query.customerId}, '%')
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND c.customer_name like concat('%', #{query.customerName}, '%')
            </if>
            <if test="query.customerAccount != null and query.customerAccount != ''">
                AND c.customer_account like concat('%', #{query.customerAccount}, '%')
            </if>
            <if test="query.customerId != null">
                AND c.customer_id like concat('%', #{query.customerId}, '%')
            </if>
            <if test="query.source != null and query.source != ''">
                AND c.source = #{query.source}
            </if>
            <if test="query.regIp != null and query.regIp != ''">
                AND c.reg_ip LIKE concat('%', #{query.regIp}, '%')
            </if>
            <if test="query.createDate != null">
                AND (DATE(c.create_time) BETWEEN #{query.createDate[0]} AND #{query.createDate[1]})
            </if>
            <if test="query.updateDate != null">
                AND (DATE(c.update_time) BETWEEN #{query.updateDate[0]} AND #{query.updateDate[1]})
            </if>
        ) data WHERE data.rn = 1
        ORDER BY data.customer_id DESC
    </select>

    <resultMap id="CustomerVoMap" type="com.yooa.crm.api.domain.vo.FriendCustomerDetailsVo">
        <association property="friendVo" javaType="com.yooa.crm.api.domain.vo.FriendVo">        <!--好友信息-->
            <id property="friendId" column="friend_id" />
            <result property="friendName" column="f_name" />
            <result property="contactMode" column="contact_mode" />
            <result property="contactPhone" column="contact_phone" />
            <result property="age" column="age" />
            <result property="sex" column="sex" />
            <result property="area" column="area" />
            <result property="matrimony" column="matrimony" />
            <result property="demand" column="demand" />
            <result property="workType" column="work_type" />
            <result property="language" column="language" />
            <result property="status" column="status" />
            <result property="incomeLevel" column="income_level" />
            <result property="projectedConsumption" column="projected_consumption" />
            <result property="remark" column="remark" />
            <result property="mainChannelId" column="main_channel_id" />
            <result property="subChannelId" column="sub_channel_id" />
            <result property="pitcherId" column="pitcher_id" />
            <result property="receiveNumber" column="receive_number" />
            <result property="loseTime" column="lose_time" jdbcType="TIMESTAMP"/>
            <result property="recordDate" column="record_date" jdbcType="DATE"/>
            <result property="extendId" column="extend_id" />
            <result property="fansType" column="fans_type" />
            <result property="createBy" column="f_create_by"/>
            <result property="createTime" column="f_create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="f_update_by" />
            <result property="updateTime" column="f_update_time"  jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" />

            <result property="channelUserName" column="channel_user_name" jdbcType="VARCHAR"/>
            <result property="channelNickName" column="channel_nick_name" jdbcType="VARCHAR"/>
            <result property="mainChannelName" column="main_channel_name" jdbcType="VARCHAR"/>
            <result property="subChannelName" column="sub_channel_name" jdbcType="VARCHAR"/>
            <association property="receiveMessage" javaType="com.yooa.crm.api.domain.vo.ExtendVo">     <!--领取人信息(现维护团队信息)-->
                <result property="userId" column="extend_id" jdbcType="BIGINT"/>
                <result property="userName" column="receive_user_name" jdbcType="VARCHAR"/>
                <result property="nickName" column="receive_nick_name" jdbcType="VARCHAR"/>
                <result property="deptId" column="receive_dept_id" jdbcType="BIGINT"/>
                <result property="deptName" column="receive_dept_name" jdbcType="VARCHAR"/>
                <result property="ancestorsNames" column="receive_ancestors_names" jdbcType="VARCHAR"/>
            </association>
            <association property="createMessage" javaType="com.yooa.crm.api.domain.vo.ExtendVo">       <!--创建人信息(录入团队信息)-->
                <result property="userId" column="f_create_by" jdbcType="BIGINT"/>
                <result property="userName" column="create_user_name" jdbcType="VARCHAR"/>
                <result property="nickName" column="create_nick_name" jdbcType="VARCHAR"/>
                <result property="deptId" column="create_dept_id" jdbcType="BIGINT"/>
                <result property="deptName" column="create_dept_name" jdbcType="VARCHAR"/>
                <result property="ancestorsNames" column="create_ancestors_names" jdbcType="VARCHAR"/>
            </association>
        </association>
    </resultMap>

    <resultMap id="customerVoMap" type="com.yooa.crm.api.domain.vo.CustomerVo">
        <id property="customerId" column="customer_id" jdbcType="BIGINT"/>
        <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
        <result property="customerAccount" column="customer_account" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="regIp" column="reg_ip" jdbcType="VARCHAR"/>
        <result property="region" column="region" jdbcType="VARCHAR"/>
        <result property="source" column="source" jdbcType="VARCHAR"/>
        <result property="uuidDevice" column="uuid_device" jdbcType="VARCHAR"/>
        <result property="lastLoginTime" column="last_login_time" jdbcType="TIMESTAMP"/>
        <result property="extendId" column="extend_id" jdbcType="BIGINT"/>
        <result property="serveId" column="serve_id" jdbcType="BIGINT"/>
        <result property="createTime" column="c_create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="c_update_time" jdbcType="TIMESTAMP"/>

        <result property="totalUp" column="totalUp" jdbcType="DECIMAL"/>
        <result property="totalUpNumber" column="totalUpNumber" jdbcType="INTEGER"/>
        <result property="monthUp" column="monthUp" jdbcType="DECIMAL"/>
        <result property="monthUpNumber" column="monthUpNumber" jdbcType="INTEGER"/>
        <result property="lostUpDate" column="lostUpDate" jdbcType="TIMESTAMP"/>
        <association property="serve" javaType="com.yooa.crm.api.domain.vo.ServeVo">     <!--VIP归属-->
            <id property="serveId" column="serve_id" jdbcType="BIGINT"/>
            <result property="userId" column="serve_user_id" jdbcType="BIGINT"/>
            <result property="userName" column="serve_user_name" jdbcType="VARCHAR"/>
            <result property="nickName" column="serve_nick_name" jdbcType="VARCHAR"/>
            <result property="deptId" column="serve_dept_id" jdbcType="BIGINT"/>
            <result property="deptName" column="serve_dept_name" jdbcType="VARCHAR"/>
            <result property="ancestorsNames" column="serve_ancestors_names" jdbcType="VARCHAR"/>
        </association>
        <collection property="anchorOperate" ofType="com.yooa.crm.api.domain.vo.AnchorOperateVo" column="customer_id"  select="selectAnchorOperateVoList"/>     <!--运营归属-->
    </resultMap>

    <select id="selectAnchorOperateVoList" resultType="com.yooa.crm.api.domain.vo.AnchorOperateVo">
        SELECT
            ao.operate_id,
            a.anchor_id,
            a.anchor_name,
            u.user_id,
            u.user_name,
            u.nick_name,
            d.dept_id,
            d.dept_name,
            d.ancestors_names
        FROM
            crm_customer_join_anchor cja
                LEFT JOIN crm_anchor a ON a.anchor_id = cja.anchor_id
                LEFT JOIN crm_anchor_operate ao ON ao.anchor_id = a.anchor_id
                LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = ao.operate_id
                LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
                LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        where cja.status = 1 AND cja.customer_id = #{customer_id}
    </select>

    <!--好友及扩展信息字段-->
    <sql id="FriendDtoSql_f">
        f.friend_id, f.friend_name AS f_name, f.contact_mode, f.contact_phone, f.age, f.sex, f.area, f.matrimony, f.demand, f.work_type,
        f.language, f.status, f.income_level, f.projected_consumption, f.remark,main_channel_id, f.sub_channel_id, f.pitcher_id,
        f.receive_number, f.lose_time, f.record_date, f.extend_id, f.fans_type,
        f.create_by AS f_create_by, f.create_time AS f_create_time, f.update_by AS f_update_by, f.update_time AS f_update_time,
        u2.user_name AS receive_user_name, u2.nick_name AS receive_nick_name, d2.dept_id AS receive_dept_id, d2.dept_name AS receive_dept_name, d2.ancestors_names AS receive_ancestors_names,
        u3.user_name AS create_user_name, u2.nick_name AS create_nick_name, d3.dept_id AS create_dept_id, d3.dept_name AS create_dept_name, d3.ancestors_names AS create_ancestors_names,
        u4.user_name AS channel_user_name, u4.nick_name AS channel_nick_name, fc1.channel_name AS main_channel_name, fc2.channel_name AS sub_channel_name
    </sql>

    <!--客户及扩展信息字段-->
    <sql id="FriendDtoSql_c">
        <!-- TODO extendId拆分 -->
        c.customer_id, c.customer_name, c.customer_account, c.status, c.reg_ip, c.region, c.source, c.uuid_device, c.last_login_time, c.extend_id,
        c.serve_id, c.create_time AS c_create_time, c.update_time AS c_update_time,
        up1.pd_user_id AS serve_id, u1.user_id AS serve_user_id, u1.user_name AS serve_user_name, u1.nick_name AS serve_nick_name, d1.dept_id AS serve_dept_id, d1.dept_name AS serve_dept_name, d1.ancestors_names AS serve_ancestors_names
    </sql>

    <select id="selCustomerVoList" resultMap="customerVoMap">
        SELECT
            <include refid="FriendDtoSql_c"/>,
            IFNULL(SUM(o1.order_money), 0)                  AS totalUp,
            IFNULL(COUNT(o1.order_id), 0)                   AS totalUpNumber,
            IFNULL(SUM(CASE WHEN date_format(o1.order_time, '%Y%m') = date_format(now(), '%Y%m') THEN o1.order_money ELSE 0 END), 0) AS monthUp,
            IFNULL(SUM(CASE WHEN date_format(o1.order_time, '%Y%m') = date_format(now(), '%Y%m') THEN 1 ELSE 0 END), 0) AS monthUpNumber,
            MAX(o1.order_time) AS lostUpDate
        FROM crm_customer AS c
        RIGHT JOIN crm_customer_friend AS cf ON c.customer_id = cf.customer_id
        LEFT JOIN yooa_system.sys_user_pd up1 ON c.serve_id = up1.pd_user_id
        LEFT JOIN yooa_system.sys_user u1 ON up1.user_id = u1.user_id
        LEFT JOIN yooa_system.sys_dept d1 ON u1.dept_id = d1.dept_id
        LEFT JOIN crm_customer_order o1 ON o1.customer_id = c.customer_id
        WHERE cf.friend_id = #{id}
        GROUP BY c.customer_id
    </select>

    <select id="selFriendCustomerVo" resultMap="CustomerVoMap">
        SELECT
               <include refid="FriendDtoSql_f"/>
        FROM crm_customer_friend AS cf
        RIGHT JOIN crm_friend AS f ON cf.friend_id = f.friend_id
        LEFT JOIN yooa_system.sys_user u2 ON f.extend_id = u2.user_id
        LEFT JOIN yooa_system.sys_dept d2 ON u2.dept_id = d2.dept_id
        LEFT JOIN yooa_system.sys_user u3 ON f.create_by = u3.user_id
        LEFT JOIN yooa_system.sys_dept d3 ON u3.dept_id = d3.dept_id
        LEFT JOIN yooa_system.sys_user u4 ON f.pitcher_id = u4.user_id
        LEFT JOIN yooa_system.sys_dept d4 ON u4.dept_id = d4.dept_id
        LEFT JOIN crm_friend_channel fc1 ON fc1.channel_id = f.main_channel_id
        LEFT JOIN crm_friend_channel fc2 ON fc2.channel_id = f.sub_channel_id
        WHERE f.friend_id = #{id}
        GROUP BY f.friend_id
    </select>
    <select id="getCustomerNameByCustomerIds" resultType="com.yooa.crm.api.domain.CrmCustomer">
       SELECT
           customer_id,customer_name
       FROM
           crm_customer
       WHERE customer_id IN
        <foreach collection="ids" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
       GROUP BY customer_id
    </select>
    <select id="getCustomerIdsByName" resultType="java.lang.Long">
        SELECT customer_id FROM crm_customer WHERE customer_name LIKE CONCAT('%', #{queryId}, '%')
    </select>


    <select id="getCustomerDataTotalPerformance"
            resultMap="CustomerDataTotalPerformanceResult">
        SELECT
            f.friend_name AS customerName,
            c.customer_name AS customerNickName,
            c.customer_id customerId,
            SUM( co.order_money ) sumOrderMoney,
            SUM( CASE WHEN DATE_FORMAT( complete_time, '%Y-%m' ) = DATE_FORMAT( NOW(), '%Y-%m' ) THEN order_money ELSE 0 END ) monthOrderMoney,
            u.nick_name extendName,
            d.dept_name extendDeptName,
            d.ancestors_names extendAncestorsNames,
            u2.nick_name serveName,
            d2.dept_name serveDeptName,
            d2.ancestors_names serveAncestorsNames
        FROM
          crm_customer_order co
                INNER JOIN crm_customer c ON c.customer_id = co.customer_id
                INNER JOIN ( SELECT customer_id, friend_id FROM crm_customer_friend WHERE `status` = 0 ) cf ON c.customer_id = cf.customer_id
                LEFT JOIN crm_friend f ON cf.friend_id = f.friend_id
                LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = c.extend_id
                LEFT JOIN yooa_system.sys_user u ON u.user_id = up.user_id
                LEFT JOIN yooa_system.sys_dept d ON d.dept_id = u.dept_id
                LEFT JOIN yooa_system.sys_user_pd up2 ON up2.pd_user_id = c.serve_id
                LEFT JOIN yooa_system.sys_user u2 ON u2.user_id = up2.user_id
                LEFT JOIN yooa_system.sys_dept d2 ON d2.dept_id = u2.dept_id
        WHERE co.order_status = 1
        <if test="query.queryId != null and query.queryId != ''">
            AND (
                c.customer_id = #{query.queryId}
                OR f.friend_name LIKE CONCAT('%',#{query.queryId},'%')
                OR u.nick_name LIKE CONCAT('%',#{query.queryId},'%')
                OR c.customer_name LIKE CONCAT('%',#{query.queryId},'%')
            )
        </if>
        <if test="query.monthMinRecharge != null">
            AND co.monthOrderMoney >= #{query.monthMinRecharge}
        </if>
        <if test="query.monthMaxRecharge != null">
            AND co.monthOrderMoney &lt;= #{query.monthMaxRecharge}
        </if>
        <if test="query.totalMinRecharge != null">
            AND co.sumOrderMoney >= #{query.totalMinRecharge}
        </if>
        <if test="query.totalMaxRecharge != null">
            AND co.sumOrderMoney &lt;= #{query.totalMaxRecharge}
        </if>
        <if test="query.deptId != null">
            AND (
                (FIND_IN_SET(#{query.deptId},d.ancestors))
            OR d.dept_id = #{query.deptId}
                )
            OR (
            (FIND_IN_SET(#{query.deptId},d2.ancestors))
            OR d2.dept_id = #{query.deptId}
            )
        </if>
        GROUP BY
        co.customer_id
    </select>

    <update id="updateQualityStatus">
        UPDATE crm_customer_friend cf
        JOIN (
            WITH TimeRange AS (
                SELECT
                    id,
                    customer_id,
                    begin_time,
                    CASE
                    WHEN begin_time > end_time THEN NULL
                    WHEN DATE_ADD(begin_time, INTERVAL 3 DAY) > end_time THEN end_time
                    ELSE DATE_ADD(begin_time, INTERVAL 3 DAY)
                    END AS sss_time
                FROM
                    crm_customer_friend
                WHERE
                    (begin_time &lt;= end_time OR end_time IS NULL) AND quality_time IS NULL
            ),
            FilteredView AS (
                SELECT
                    vlt.customer_id,
                    vlt.view_time,
                    vlt.enter_time,
                    tr.id AS friend_record_id,
                    tr.begin_time,
                    tr.sss_time
                FROM
                    crm_customer_view_live_time vlt
                JOIN
                    TimeRange tr ON vlt.customer_id = tr.customer_id AND vlt.view_time BETWEEN tr.begin_time AND tr.sss_time
            ),
            CumulativeEnterTime AS (
                SELECT
                    fv.customer_id,
                    fv.friend_record_id,
                    fv.view_time,
                    fv.enter_time,
                    SUM(fv.enter_time) OVER (PARTITION BY fv.customer_id, fv.friend_record_id ORDER BY fv.view_time) AS cumulative_enter_time
                FROM
                    FilteredView fv
            ),
            FirstExceeding600 AS (
                SELECT
                    cet.customer_id,
                    cet.friend_record_id,
                    cet.view_time,
                    ROW_NUMBER() OVER (PARTITION BY cet.customer_id, cet.friend_record_id ORDER BY cet.view_time) AS rn
                FROM
                    CumulativeEnterTime cet
                WHERE
                    cet.cumulative_enter_time > 600
            )
            SELECT
                fe.customer_id,
                fe.view_time,
                fe.friend_record_id
            FROM
                FirstExceeding600 fe
            WHERE
                fe.rn = 1
        ) cf1 ON cf.id = cf1.friend_record_id
        SET cf.quality_time = cf1.view_time;
    </update>


    <select id="orderUserGroup" resultType="com.yooa.extend.api.domain.vo.ExtendDataUserGroupVo">
        SELECT
        IFNULL( SUM( o.order_money ), 0 ) AS value,
        u.user_id AS userId,
        u.nick_name AS nickName
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        LEFT JOIN (
        SELECT
        SUM(o.order_money) AS order_money,
        extend_id
        FROM crm_customer_order o
        WHERE
        o.order_status = 1
        <if test="dto.beginTime != null">
            AND o.order_time >= #{dto.beginTime}
        </if>
        <if test="dto.endTime != null">
            AND o.order_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.deptId != null and dto.deptId != 0">
            AND o.extend_dept_id = #{dto.deptId}
        </if>
        <if test="dto.userId != null and dto.userId != 0">
            AND o.extend_id = #{dto.userId}
        </if>
        GROUP BY extend_id
        ) AS o ON u.user_id = o.extend_id
        GROUP BY u.user_id
    </select>

    <select id="addOrderUserGroup" resultType="com.yooa.extend.api.domain.vo.ExtendDataUserGroupVo">
        SELECT
        IFNULL( SUM( o.order_money ), 0 ) AS value,
        u.user_id AS userId,
        u.nick_name AS nickName
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        LEFT JOIN (
        SELECT
        SUM(o.order_money) AS order_money,
        extend_id
        FROM crm_customer_order o
        LEFT JOIN crm_customer AS c ON c.customer_id = o.customer_id AND c.extend_id = o.py_extend_id
        AND DATE_ADD(c.update_time, INTERVAL 45 DAY) > o.order_time AND c.update_time &lt; order_time
        WHERE
        o.order_status = 1
        AND c.customer_id IS NOT NULL
        <if test="dto.beginTime != null">
            AND o.order_time >= #{dto.beginTime}
        </if>
        <if test="dto.endTime != null">
            AND o.order_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.deptId != null and dto.deptId != 0">
            AND o.extend_dept_id = #{dto.deptId}
        </if>
        <if test="dto.userId != null and dto.userId != 0">
            AND o.extend_id = #{dto.userId}
        </if>
        GROUP BY extend_id
        ) AS o ON u.user_id = o.extend_id
        GROUP BY u.user_id
    </select>

    <select id="friendUserGroup" resultType="com.yooa.extend.api.domain.vo.ExtendDataUserGroupVo">
        SELECT
        IFNULL( SUM( f.friend_id ), 0 ) AS value,
        u.user_id AS userId,
        u.nick_name AS nickName
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        LEFT JOIN (
        SELECT
        COUNT(DISTINCT f.friend_id ) AS friend_id,
        f.extend_id
        FROM crm_friend AS f
        <where>
            <if test="dto.beginTime != null">
                AND f.create_time >= #{dto.beginTime}
            </if>
            <if test="dto.endTime != null">
                AND f.create_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.userId != null and dto.userId != 0">
                AND f.extend_id = #{dto.userId}
            </if>
        </where>
        GROUP BY f.extend_id
        ) AS f ON f.extend_id = u.user_id
        GROUP BY u.user_id
    </select>

    <select id="registerUserGroup" resultType="com.yooa.extend.api.domain.vo.ExtendDataUserGroupVo">
        SELECT
        IFNULL( SUM( r.register ), 0 ) AS value,
        u.user_id AS userId,
        u.nick_name AS nickName
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        LEFT JOIN (
        SELECT
        COUNT(DISTINCT cf.friend_id) AS register,
        u.user_id
        FROM crm_customer_friend AS cf
        LEFT JOIN yooa_system.sys_user_pd AS up ON cf.py_extend_id = up.pd_user_id
        LEFT JOIN yooa_system.sys_user AS u ON up.user_id = u.user_id
        <where>
            <if test="dto.beginTime != null">
                AND cf.begin_time >= #{dto.beginTime}
            </if>
            <if test="dto.endTime != null">
                AND cf.begin_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.userId != null and dto.userId != 0">
                AND u.user_id = #{dto.userId}
            </if>
        </where>
        GROUP BY u.user_id
        ) AS r ON u.user_id = r.user_id
        GROUP BY u.user_id
    </select>

    <select id="qualityUsersUserGroup" resultType="com.yooa.extend.api.domain.vo.ExtendDataUserGroupVo">
        SELECT
        IFNULL( SUM( r.qualityUsers ), 0 ) AS value,
        u.user_id AS userId,
        u.nick_name AS nickName
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        LEFT JOIN (
        SELECT
        COUNT(DISTINCT cf.friend_id) AS qualityUsers,
        u.user_id
        FROM crm_customer_friend AS cf
        LEFT JOIN yooa_system.sys_user_pd AS up ON cf.py_extend_id = up.pd_user_id
        LEFT JOIN yooa_system.sys_user AS u ON up.user_id = u.user_id
        WHERE
        cf.quality_time IS NOT NULL
        <if test="dto.beginTime != null">
            AND cf.begin_time >= #{dto.beginTime}
        </if>
        <if test="dto.endTime != null">
            AND cf.begin_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.userId != null and dto.userId != 0">
            AND u.user_id = #{dto.userId}
        </if>
        GROUP BY u.user_id
        ) AS r ON u.user_id = r.user_id
        GROUP BY u.user_id
    </select>

    <select id="handoverUserGroup" resultType="com.yooa.extend.api.domain.vo.ExtendDataUserGroupVo">
        SELECT
        IFNULL( SUM( cch.register ), 0 ) AS value,
        u.user_id AS userId,
        u.nick_name AS nickName
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        LEFT JOIN (
        SELECT
        COUNT(DISTINCT cf.friend_id) AS register,
        cch.extend_id
        FROM crm_customer_handover AS cch
        LEFT JOIN crm_customer_friend AS cf ON cch.customer_friend_id = cf.id
        WHERE
        cch.customer_friend_id IS NOT NULL
        AND cch.handover_type = #{type}
        AND cch.handover_status = 1
        <if test="dto.beginTime != null">
            AND cch.receive_time >= #{dto.beginTime}
        </if>
        <if test="dto.endTime != null">
            AND cch.receive_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.userId != null and dto.userId != 0">
            AND cch.extend_id = #{dto.userId}
        </if>
        GROUP BY cch.extend_id
        ) AS cch ON cch.extend_id = u.user_id
        GROUP BY u.user_id
    </select>

    <select id="extendVermicelliUserGroup" resultType="com.yooa.extend.api.domain.vo.ExtendDataUserGroupVo">
        SELECT
        IFNULL( SUM( e.vermicelliNumber ), 0 ) AS value,
        u.user_id AS userId,
        u.nick_name AS nickName
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        LEFT JOIN (
        SELECT
        COUNT(e.id) AS vermicelliNumber,
        e.extend_id
        FROM extend_vermicelli AS e
        WHERE
        e.fans_type = #{type}
        <if test="dto.beginTime != null">
            AND e.record_date >= #{dto.beginTime}
        </if>
        <if test="dto.endTime != null">
            AND e.record_date &lt;= #{dto.endTime}
        </if>
        <if test="dto.userId != null and dto.userId != 0">
            AND e.extend_id = #{dto.userId}
        </if>
        GROUP BY e.extend_id
        ) AS e ON e.extend_id = u.user_id
        GROUP BY u.user_id
    </select>

    <select id="orderDeptGroup" resultType="com.yooa.extend.api.domain.vo.ExtendDataDeptGroupVo">
        SELECT
        IFNULL( SUM( o.order_money ), 0 ) AS value,
        COUNT(DISTINCT u.user_id) AS peopleNumber,
        d.dept_id,
        d.dept_name
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        LEFT JOIN (
        SELECT
        SUM(o.order_money) AS order_money,
        extend_dept_id
        FROM crm_customer_order o
        WHERE
        o.order_status = 1
        <if test="dto.beginTime != null">
            AND o.order_time >= #{dto.beginTime}
        </if>
        <if test="dto.endTime != null">
            AND o.order_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.deptId != null and dto.deptId != 0">
            AND o.extend_dept_id = #{dto.deptId}
        </if>
        GROUP BY extend_dept_id
        ) AS o ON FIND_IN_SET(o.extend_dept_id,d.juniorIds)
        GROUP BY d.dept_id
    </select>

    <select id="addOrderDeptGroup" resultType="com.yooa.extend.api.domain.vo.ExtendDataDeptGroupVo">
        SELECT
        IFNULL( SUM( o.order_money ), 0 ) AS value,
        COUNT(DISTINCT u.user_id) AS peopleNumber,
        d.dept_id,
        d.dept_name
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        LEFT JOIN (
        SELECT
        SUM(o.order_money) AS order_money,
        extend_dept_id
        FROM crm_customer_order o
        LEFT JOIN crm_customer AS c ON c.customer_id = o.customer_id AND c.extend_id = o.py_extend_id
        AND DATE_ADD(c.update_time, INTERVAL 45 DAY) > o.order_time AND c.update_time &lt; order_time
        WHERE
        o.order_status = 1
        AND c.customer_id IS NOT NULL
        <if test="dto.beginTime != null">
            AND o.order_time >= #{dto.beginTime}
        </if>
        <if test="dto.endTime != null">
            AND o.order_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.deptId != null and dto.deptId != 0">
            AND o.extend_dept_id = #{dto.deptId}
        </if>
        GROUP BY extend_dept_id
        ) AS o ON FIND_IN_SET(o.extend_dept_id,d.juniorIds)
        GROUP BY d.dept_id
    </select>

    <select id="friendDeptGroup" resultType="com.yooa.extend.api.domain.vo.ExtendDataDeptGroupVo">
        SELECT
        IFNULL( SUM( f.friend_id ), 0 ) AS value,
        COUNT(DISTINCT u.user_id) AS peopleNumber,
        d.dept_id,
        d.dept_name
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        LEFT JOIN (
        SELECT
        COUNT(DISTINCT f.friend_id ) AS friend_id,
        u.dept_id
        FROM crm_friend AS f
        LEFT JOIN yooa_system.sys_user AS u on u.user_id = f.extend_id
        <where>
            <if test="dto.beginTime != null">
                AND f.create_time >= #{dto.beginTime}
            </if>
            <if test="dto.endTime != null">
                AND f.create_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.deptId != null and dto.deptId != 0">
                AND u.dept_id = #{dto.deptId}
            </if>
        </where>
        GROUP BY u.dept_id
        ) AS f ON FIND_IN_SET(f.dept_id,d.juniorIds)
        GROUP BY d.dept_id
    </select>

    <select id="registerDeptGroup" resultType="com.yooa.extend.api.domain.vo.ExtendDataDeptGroupVo">
        SELECT
        IFNULL( SUM( r.register ), 0 ) AS value,
        COUNT(DISTINCT u.user_id) AS peopleNumber,
        d.dept_id,
        d.dept_name
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        LEFT JOIN (
        SELECT
        COUNT(DISTINCT cf.friend_id) AS register,
        u.dept_id
        FROM crm_customer_friend AS cf
        LEFT JOIN yooa_system.sys_user_pd AS up ON cf.py_extend_id = up.pd_user_id
        LEFT JOIN yooa_system.sys_user AS u ON up.user_id = u.user_id
        <where>
            <if test="dto.beginTime != null">
                AND cf.begin_time >= #{dto.beginTime}
            </if>
            <if test="dto.endTime != null">
                AND cf.begin_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.deptId != null and dto.deptId != 0">
                AND u.dept_id = #{dto.deptId}
            </if>
        </where>
        GROUP BY u.dept_id
        ) AS r ON FIND_IN_SET(r.dept_id,d.juniorIds)
        GROUP BY d.dept_id
    </select>

    <select id="qualityUsersDeptGroup" resultType="com.yooa.extend.api.domain.vo.ExtendDataDeptGroupVo">
        SELECT
        IFNULL( SUM( r.qualityUsers ), 0 ) AS value,
        COUNT(DISTINCT u.user_id) AS peopleNumber,
        d.dept_id,
        d.dept_name
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        LEFT JOIN (
        SELECT
        COUNT(DISTINCT cf.friend_id) AS qualityUsers,
        u.dept_id
        FROM crm_customer_friend AS cf
        LEFT JOIN yooa_system.sys_user_pd AS up ON cf.py_extend_id = up.pd_user_id
        LEFT JOIN yooa_system.sys_user AS u ON up.user_id = u.user_id
        WHERE
        cf.quality_time IS NOT NULL
        <if test="dto.beginTime != null">
            AND cf.begin_time >= #{dto.beginTime}
        </if>
        <if test="dto.endTime != null">
            AND cf.begin_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.deptId != null and dto.deptId != 0">
            AND u.dept_id = #{dto.deptId}
        </if>
        GROUP BY u.dept_id
        ) AS r ON FIND_IN_SET(r.dept_id,d.juniorIds)
        GROUP BY d.dept_id
    </select>

    <select id="handoverDeptGroup" resultType="com.yooa.extend.api.domain.vo.ExtendDataDeptGroupVo">
        SELECT
        IFNULL( SUM( cch.register ), 0 ) AS value,
        COUNT(DISTINCT u.user_id) AS peopleNumber,
        d.dept_id,
        d.dept_name
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        LEFT JOIN (
        SELECT
        COUNT(DISTINCT cf.friend_id) AS register,
        u.dept_id
        FROM crm_customer_handover AS cch
        LEFT JOIN crm_customer_friend AS cf ON cch.customer_friend_id = cf.id
        LEFT JOIN yooa_system.sys_user AS u ON cch.extend_id = u.user_id
        WHERE
        cch.customer_friend_id IS NOT NULL
        AND cch.handover_type = #{type}
        AND cch.handover_status = 1
        <if test="dto.beginTime != null">
            AND cch.receive_time >= #{dto.beginTime}
        </if>
        <if test="dto.endTime != null">
            AND cch.receive_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.deptId != null and dto.deptId != 0">
            AND u.dept_id = #{dto.deptId}
        </if>
        GROUP BY u.dept_id
        ) AS cch ON FIND_IN_SET(cch.dept_id,d.juniorIds)
        GROUP BY d.dept_id
    </select>

    <select id="extendVermicelliDeptGroup" resultType="com.yooa.extend.api.domain.vo.ExtendDataDeptGroupVo">
        SELECT
        IFNULL( SUM( e.vermicelliNumber ), 0 ) AS value,
        COUNT(DISTINCT u.user_id) AS peopleNumber,
        d.dept_id,
        d.dept_name
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        LEFT JOIN (
        SELECT
        COUNT(e.id) AS vermicelliNumber,
        e.extend_dept_id
        FROM extend_vermicelli AS e
        WHERE
        e.fans_type = #{type}
        <if test="dto.beginTime != null">
            AND e.record_date >= #{dto.beginTime}
        </if>
        <if test="dto.endTime != null">
            AND e.record_date &lt;= #{dto.endTime}
        </if>
        <if test="dto.deptId != null and dto.deptId != 0">
            AND e.extend_dept_id = #{dto.deptId}
        </if>
        GROUP BY e.extend_dept_id
        ) AS e ON FIND_IN_SET(e.extend_dept_id,d.juniorIds)
        GROUP BY d.dept_id
    </select>

    <select id="orderTimeGroup" resultType="com.yooa.extend.api.domain.vo.ExtendDataTimeGroupVo">
        SELECT
        IFNULL( SUM( o.order_money ), 0 ) AS value,
        o.order_time AS date
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN (
        SELECT
        SUM(o.order_money) AS order_money,
        extend_dept_id
        <choose>
            <when test="dto.expType != null and dto.expType == 0">
                ,CONCAT(DATE_FORMAT(o.order_time, '%Y-%m'), '-01') AS order_time
            </when>
            <otherwise>
                ,DATE(o.order_time) AS order_time
            </otherwise>
        </choose>
        FROM crm_customer_order o
        WHERE
        o.order_status = 1
        <if test="dto.beginTime != null">
            AND o.order_time >= #{dto.beginTime}
        </if>
        <if test="dto.endTime != null">
            AND o.order_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.deptId != null and dto.deptId != 0">
            AND o.extend_dept_id = #{dto.deptId}
        </if>
        GROUP BY extend_dept_id
        <choose>
            <when test="dto.expType != null and dto.expType == 0">
                DATE_FORMAT(o.order_time, '%Y-%m')
            </when>
            <otherwise>
                DATE(o.order_time)
            </otherwise>
        </choose>
        ) AS o ON FIND_IN_SET(o.extend_dept_id,d.juniorIds)
        GROUP BY o.order_time
    </select>

    <select id="addOrderTimeGroup" resultType="com.yooa.extend.api.domain.vo.ExtendDataTimeGroupVo">
        SELECT
        IFNULL( SUM( o.order_money ), 0 ) AS value,
        o.order_time AS date
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN (
        SELECT
        SUM(o.order_money) AS order_money,
        extend_dept_id
        <choose>
            <when test="dto.expType != null and dto.expType == 0">
                ,CONCAT(DATE_FORMAT(o.order_time, '%Y-%m'), '-01') AS order_time
            </when>
            <otherwise>
                ,DATE(o.order_time) AS order_time
            </otherwise>
        </choose>
        FROM crm_customer_order o
        LEFT JOIN crm_customer AS c ON c.customer_id = o.customer_id AND c.extend_id = o.py_extend_id
        AND DATE_ADD(c.update_time, INTERVAL 45 DAY) > o.order_time AND c.update_time &lt; order_time
        WHERE
        o.order_status = 1
        AND c.customer_id IS NOT NULL
        <if test="dto.beginTime != null">
            AND o.order_time >= #{dto.beginTime}
        </if>
        <if test="dto.endTime != null">
            AND o.order_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.deptId != null and dto.deptId != 0">
            AND o.extend_dept_id = #{dto.deptId}
        </if>
        GROUP BY extend_dept_id,
        <choose>
            <when test="dto.expType != null and dto.expType == 0">
                DATE_FORMAT(o.order_time, '%Y-%m')
            </when>
            <otherwise>
                DATE(o.order_time)
            </otherwise>
        </choose>
        ) AS o ON FIND_IN_SET(o.extend_dept_id,d.juniorIds)
        GROUP BY o.order_time
    </select>

    <select id="friendTimeGroup" resultType="com.yooa.extend.api.domain.vo.ExtendDataTimeGroupVo">
        SELECT
        IFNULL( SUM( f.friend_id ), 0 ) AS value,
        f.create_time AS date
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN (
        SELECT
        COUNT(DISTINCT f.friend_id ) AS friend_id,
        u.dept_id
        <choose>
            <when test="dto.expType != null and dto.expType == 0">
                ,CONCAT(DATE_FORMAT(f.create_time, '%Y-%m'), '-01') AS begin_time
            </when>
            <otherwise>
                ,DATE(f.create_time) AS begin_time
            </otherwise>
        </choose>
        FROM crm_friend AS f
        LEFT JOIN yooa_system.sys_user AS u on u.user_id = f.extend_id
        <where>
            <if test="dto.beginTime != null">
                AND f.create_time >= #{dto.beginTime}
            </if>
            <if test="dto.endTime != null">
                AND f.create_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.deptId != null and dto.deptId != 0">
                AND u.dept_id = #{dto.deptId}
            </if>
        </where>
        GROUP BY u.dept_id,
        <choose>
            <when test="dto.expType != null and dto.expType == 0">
                DATE_FORMAT(f.create_time, '%Y-%m')
            </when>
            <otherwise>
                DATE(f.create_time)
            </otherwise>
        </choose>
        ) AS f ON FIND_IN_SET(f.dept_id,d.juniorIds)
        GROUP BY f.create_time
    </select>

    <select id="registerTimeGroup" resultType="com.yooa.extend.api.domain.vo.ExtendDataTimeGroupVo">
        SELECT
        IFNULL( SUM( r.register ), 0 ) AS value,
        r.begin_time AS date
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN (
        SELECT
        COUNT(DISTINCT cf.friend_id) AS register,
        u.dept_id
        <choose>
            <when test="dto.expType != null and dto.expType == 0">
                ,CONCAT(DATE_FORMAT(cf.begin_time, '%Y-%m'), '-01') AS begin_time
            </when>
            <otherwise>
                ,DATE(cf.begin_time) AS begin_time
            </otherwise>
        </choose>
        FROM crm_customer_friend AS cf
        LEFT JOIN yooa_system.sys_user_pd AS up ON cf.py_extend_id = up.pd_user_id
        LEFT JOIN yooa_system.sys_user AS u ON up.user_id = u.user_id
        <where>
            <if test="dto.beginTime != null">
                AND cf.begin_time >= #{dto.beginTime}
            </if>
            <if test="dto.endTime != null">
                AND cf.begin_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.deptId != null and dto.deptId != 0">
                AND u.dept_id = #{dto.deptId}
            </if>
        </where>
        GROUP BY u.dept_id,
        <choose>
            <when test="dto.expType != null and dto.expType == 0">
                DATE_FORMAT(cf.begin_time, '%Y-%m')
            </when>
            <otherwise>
                DATE(cf.begin_time)
            </otherwise>
        </choose>
        ) AS r ON FIND_IN_SET(r.dept_id,d.juniorIds)
        GROUP BY r.begin_time
    </select>

    <select id="qualityUsersTimeGroup" resultType="com.yooa.extend.api.domain.vo.ExtendDataTimeGroupVo">
        SELECT
        IFNULL( SUM( r.qualityUsers ), 0 ) AS value,
        r.begin_time AS date
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN (
        SELECT
        COUNT(DISTINCT cf.friend_id) AS qualityUsers,
        u.dept_id
        <choose>
            <when test="dto.expType != null and dto.expType == 0">
                ,CONCAT(DATE_FORMAT(cf.begin_time, '%Y-%m'), '-01') AS begin_time
            </when>
            <otherwise>
                ,DATE(cf.begin_time) AS begin_time
            </otherwise>
        </choose>
        FROM crm_customer_friend AS cf
        LEFT JOIN yooa_system.sys_user_pd AS up ON cf.py_extend_id = up.pd_user_id
        LEFT JOIN yooa_system.sys_user AS u ON up.user_id = u.user_id
        WHERE
        cf.quality_time IS NOT NULL
        <if test="dto.beginTime != null">
            AND cf.begin_time >= #{dto.beginTime}
        </if>
        <if test="dto.endTime != null">
            AND cf.begin_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.deptId != null and dto.deptId != 0">
            AND u.dept_id = #{dto.deptId}
        </if>
        GROUP BY u.dept_id,
        <choose>
            <when test="dto.expType != null and dto.expType == 0">
                DATE_FORMAT(cf.begin_time, '%Y-%m')
            </when>
            <otherwise>
                DATE(cf.begin_time)
            </otherwise>
        </choose>
        ) AS r ON FIND_IN_SET(r.dept_id,d.juniorIds)
        GROUP BY r.begin_time
    </select>

    <select id="handoverTimeGroup" resultType="com.yooa.extend.api.domain.vo.ExtendDataTimeGroupVo">
        SELECT
        IFNULL( SUM( cch.register ), 0 ) AS value,
        cch.receive_time AS date
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN (
        SELECT
        COUNT(DISTINCT cf.friend_id) AS register,
        u.dept_id
        <choose>
            <when test="dto.expType != null and dto.expType == 0">
                ,CONCAT(DATE_FORMAT(cch.receive_time, '%Y-%m'), '-01') AS receive_time
            </when>
            <otherwise>
                ,DATE(cch.receive_time) AS receive_time
            </otherwise>
        </choose>
        FROM crm_customer_handover AS cch
        LEFT JOIN crm_customer_friend AS cf ON cch.customer_friend_id = cf.id
        LEFT JOIN yooa_system.sys_user AS u ON cch.extend_id = u.user_id
        WHERE
        cch.customer_friend_id IS NOT NULL
        AND cch.handover_type = #{type}
        AND cch.handover_status = 1
        <if test="dto.beginTime != null">
            AND cch.receive_time >= #{dto.beginTime}
        </if>
        <if test="dto.endTime != null">
            AND cch.receive_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.deptId != null and dto.deptId != 0">
            AND u.dept_id = #{dto.deptId}
        </if>
        GROUP BY u.dept_id,
        <choose>
            <when test="dto.expType != null and dto.expType == 0">
                DATE_FORMAT(cch.receive_time, '%Y-%m')
            </when>
            <otherwise>
                DATE(cch.receive_time)
            </otherwise>
        </choose>
        ) AS cch ON FIND_IN_SET(cch.dept_id,d.juniorIds)
        GROUP BY cch.receive_time
    </select>

    <select id="extendVermicelliTimeGroup" resultType="com.yooa.extend.api.domain.vo.ExtendDataTimeGroupVo">
        SELECT
        IFNULL( SUM( e.vermicelliNumber ), 0 ) AS value,
        e.record_date AS date
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN (
        SELECT
        COUNT(e.id) AS vermicelliNumber,
        e.extend_dept_id
        <choose>
            <when test="dto.expType != null and dto.expType == 0">
                ,CONCAT(DATE_FORMAT(e.record_date, '%Y-%m'), '-01') AS record_date
            </when>
            <otherwise>
                ,e.record_date
            </otherwise>
        </choose>
        FROM extend_vermicelli AS e
        WHERE
        e.fans_type = #{type}
        <if test="dto.beginTime != null">
            AND e.record_date >= #{dto.beginTime}
        </if>
        <if test="dto.endTime != null">
            AND e.record_date &lt;= #{dto.endTime}
        </if>
        <if test="dto.deptId != null and dto.deptId != 0">
            AND e.extend_dept_id = #{dto.deptId}
        </if>
        GROUP BY e.extend_dept_id,
        <choose>
            <when test="dto.expType != null and dto.expType == 0">
                DATE_FORMAT(e.record_date, '%Y-%m')
            </when>
            <otherwise>
                e.record_date
            </otherwise>
        </choose>
        ) AS e ON FIND_IN_SET(e.extend_dept_id,d.juniorIds)
        GROUP BY e.record_date
    </select>

    <sql id="getDeptJuniorIds">    <!--获取下级部门ID集-->
        SELECT d.dept_id,d.dept_name,GROUP_CONCAT(d1.dept_id) AS juniorIds
        FROM yooa_system.sys_dept AS d
        LEFT JOIN yooa_system.sys_dept AS d1 ON FIND_IN_SET(d.dept_id,d1.ancestors) OR d.dept_id = d1.dept_id
        <where>
            d.del_flag = 0
            <if test="dto.deptId != null and dto.deptId != 0">
                AND d.dept_id = #{dto.deptId}
            </if>
            <if test="dto.selType != null and dto.selType != 0">
                AND d.dept_type = #{dto.selType}
            </if>
            <if test="dto.deptLevel != null and dto.deptLevel != 0">
                AND d.dept_level = #{dto.deptLevel}
            </if>
            <if test="dto.parentId != null and dto.parentId != 0">
                AND d.parent_id = #{dto.parentId}
            </if>
        </where>
        GROUP BY d.dept_id
    </sql>

    <select id="goalTimeGroup" resultType="com.yooa.extend.api.domain.vo.ExtendDataTimeGroupVo">
        SELECT
        IFNULL( sum(w.value), 0 ) AS value,
        FROM_UNIXTIME(w.date_weeks_begin) AS date,
        FROM_UNIXTIME(w.date_weeks_end) AS endDate
        FROM (
        SELECT DISTINCT u.user_id FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        ) AS u
        LEFT JOIN yooa_extend.goal_extend_pack AS p ON u.user_id = p.producer_id
        LEFT JOIN yooa_extend.goal_extend_weeks AS w ON w.goal_id = p.id
        WHERE
        p.goal_type = 0
        AND p.table_type = 1
        <if test="dto.fieldsGoalId != null and dto.fieldsGoalId != 0">
            AND w.fields_id = #{dto.fieldsGoalId}
        </if>
        <if test="dto.beginTime != null">
            AND FROM_UNIXTIME(w.date_weeks_begin) >= #{dto.beginTime}
        </if>
        <if test="dto.endTime != null">
            AND FROM_UNIXTIME(w.date_weeks_begin) &lt;= #{dto.endTime}
        </if>
        GROUP BY FROM_UNIXTIME(w.date_weeks_begin)
    </select>

</mapper>
