package com.yooa.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.system.api.domain.SysDept;
import com.yooa.system.api.domain.query.DeptQuery;
import com.yooa.system.api.domain.vo.GroupDeptVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 部门管理 - 数据层
 */
public interface SysDeptMapper extends BaseMapper<SysDept> {
    /**
     * 查询部门管理数据
     */
    public List<SysDept> selectDeptList(@Param("query") DeptQuery query);

    /**
     * 查询部门管理数据
     */
    public List<SysDept> selectDeptList(Page<SysDept> page, @Param("query") DeptQuery query);

    /**
     * 查询部门详情数据
     */
    public SysDept selectDeptById(@Param("deptId") Long deptId);

    /**
     * 根据角色ID查询部门树信息
     */
    public List<Long> selectDeptListByRoleId(@Param("roleId") Long roleId, @Param("deptCheckStrictly") boolean deptCheckStrictly);

    /**
     * 根据角色ID查询父部门为世纪征途的部门
     */
    public List<Long> selectSJListByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据ID查询所有子部门
     */
    public List<SysDept> selectAllChildrenDeptById(@Param("deptId") Long deptId);

    /**
     * 根据ID查询下级子部门
     */
    public List<SysDept> selectChildrenDeptById(@Param("deptId") Long deptId);

    /**
     * 根据ID查询所有子部门（正常状态）
     */
    public int selectNormalChildrenDeptById(@Param("deptId") Long deptId);

    /**
     * 是否存在子节点
     */
    public int hasChildByDeptId(@Param("deptId") Long deptId);

    /**
     * 查询部门是否存在用户
     */
    public int checkDeptExistUser(@Param("deptId") Long deptId);

    /**
     * 校验部门名称是否唯一
     */
    public SysDept checkDeptNameUnique(@Param("deptName") String deptName, @Param("parentId") Long parentId);

    /**
     * 修改所在部门正常状态
     */
    public void updateDeptStatusNormal(@Param("deptIds") Long[] deptIds);

    /**
     * 修改子元素关系
     */
    public int updateDeptChildren(@Param("deptList") List<SysDept> deptList);

    /**
     * 查询所有生效的部门
     * @return
     */
    public List<SysDept> selectAllSysDept();

}
