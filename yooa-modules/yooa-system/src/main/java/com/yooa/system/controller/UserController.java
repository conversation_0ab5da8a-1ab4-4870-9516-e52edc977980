package com.yooa.system.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.cmf.api.RemoteCmfAdminService;
import com.yooa.cmf.api.RemoteCmfUserService;
import com.yooa.cmf.api.domain.CmfAgentAdmin;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.domain.R;
import com.yooa.common.core.utils.poi.ExcelUtil;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.common.log.annotation.Log;
import com.yooa.common.log.enums.BusinessType;
import com.yooa.common.security.annotation.InnerAuth;
import com.yooa.common.security.annotation.RequiresPermissions;
import com.yooa.common.security.utils.SecurityUtils;
import com.yooa.system.api.constant.DeptConstants;
import com.yooa.system.api.domain.*;
import com.yooa.system.api.domain.dto.QueryPage;
import com.yooa.system.api.domain.dto.UserEditDto;
import com.yooa.system.api.domain.dto.UserSaveDto;
import com.yooa.system.api.domain.query.DeptQuery;
import com.yooa.system.api.domain.query.PublicUserQuery;
import com.yooa.system.api.domain.query.UserQuery;
import com.yooa.system.api.domain.vo.*;
import com.yooa.system.api.model.LoginUser;
import com.yooa.system.mapper.SysUserPdMapper;
import com.yooa.system.service.*;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@Validated
@AllArgsConstructor
@RestController
@RequestMapping("/user")
public class UserController extends BaseController {
    private final UserService userService;
    private final RoleService roleService;
    private final DeptService deptService;
    private final PostService postService;
    private final PermissionService permissionService;
    private final ConfigService configService;
    private final RosterService rosterService;
    private final RosterUserService rosterUserService;
    private final RemoteCmfAdminService remoteCmfAdminService;
    private final RemoteCmfUserService remoteCmfUserService;
    private final UserPdService userPdService;

    /**
     * 获取用户列表
     */
    @RequiresPermissions("system:user:list")
    @GetMapping("/list")
    public AjaxResult list(Page<SysUserVo> page, UserQuery query) {
        return success(page.setRecords(userService.selectUserList(page, query)));
    }

    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @RequiresPermissions("system:user:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserQuery query) {
        List<SysUserVo> list = userService.selectUserList(null, query);
        ExcelUtil<SysUserVo> util = new ExcelUtil<SysUserVo>(SysUserVo.class);
        util.exportExcel(response, list, "用户数据");
    }

    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @RequiresPermissions("system:user:import")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        List<SysUser> userList = util.importExcel(file.getInputStream());
        String message = userService.importUser(userList, updateSupport);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        util.importTemplateExcel(response, "用户数据");
    }

    /**
     * 获取当前用户信息
     */
    @InnerAuth
    @GetMapping("/info/{username}")
    public R<LoginUser> info(@PathVariable("username") String username) {
        SysUserVo sysUser = userService.selectUserByUserName(username);
        if (ObjUtil.isNull(sysUser)) {
            return R.fail("用户名或密码错误");
        }
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(sysUser.getUserId());
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(sysUser);
        LoginUser sysUserVo = new LoginUser();
        sysUserVo.setSysUser(sysUser);
        sysUserVo.setRoles(roles);
        sysUserVo.setPermissions(permissions);
        return R.ok(sysUserVo);
    }

    /**
     * 注册用户信息
     */
    @InnerAuth
    @PostMapping("/register")
    public R<Boolean> register(@RequestBody UserSaveDto sysUser) {
        String username = sysUser.getUserName();
        if (!("true".equals(configService.getConfigValueByKey("sys.account.registerUser")))) {
            return R.fail("当前系统没有开启注册功能！");
        }
        if (!userService.checkUserNameUnique(sysUser)) {
            return R.fail("保存用户'" + username + "'失败，注册账号已存在");
        }
        return R.ok(userService.save(sysUser));
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo() {
        SysUserVo user = userService.selectUserById(SecurityUtils.getUserId());
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user.getUserId());
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);

        List<SysUserVo> users = new ArrayList<>();
        SysRosterUser rosterUser = rosterUserService.getByUserId(user.getUserId());
        if (ObjUtil.isNotEmpty(rosterUser)) {
            List<SysRosterUser> rosterUsers = rosterUserService.listByRosterId(rosterUser.getRosterId());
            for (SysRosterUser ru : rosterUsers) {
                users.add(userService.selectUserById(ru.getUserId()));
            }
        }

        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("users", users);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 根据用户编号获取详细信息
     */
    @RequiresPermissions("system:user:query")
    @GetMapping(value = {"/", "/{userId}"})
    public AjaxResult getInfo(@PathVariable(value = "userId", required = false) Long userId) {
        userService.checkUserDataScope(userId);
        AjaxResult ajax = AjaxResult.success();
        List<SysRoleVo> roles = roleService.selectRoleAll();
        ajax.put("roles", SecurityUtils.isAdmin(userId) ? roles : roles.stream().filter(r -> !SecurityUtils.isAdmin(r.getRoleId())).collect(Collectors.toList()));
        ajax.put("posts", postService.list());
        if (ObjUtil.isNotNull(userId)) {
            SysUserVo sysUser = userService.selectUserById(userId);
            ajax.put(AjaxResult.DATA_TAG, sysUser);
            ajax.put("postIds", postService.selectPostListByUserId(userId));
            ajax.put("roleIds", sysUser.getRoles().stream().map(SysRole::getRoleId).collect(Collectors.toList()));
        }
        return ajax;
    }

    /**
     * 新增用户
     */
    @RequiresPermissions("system:user:add")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody UserSaveDto user) {
        if (!userService.checkUserNameUnique(user)) {
            return error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        }
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        return toAjax(userService.insertUser(user));
    }

    /**
     * 修改用户
     */
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody UserEditDto user) {
        userService.checkUserAllowed(user.getUserId());
        userService.checkUserDataScope(user.getUserId());
        if (!userService.checkUserNameUnique(user)) {
            return error("修改用户'" + user.getUserName() + "'失败，登录账号已存在");
        }
        return toAjax(userService.updateUser(user));
    }

    /**
     * 删除用户
     */
    @RequiresPermissions("system:user:remove")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable List<Long> userIds) {
        if (CollUtil.contains(userIds, SecurityUtils.getUserId())) {
            return error("当前用户不能删除");
        }
        return toAjax(userService.deleteUserByIds(userIds));
    }

    /**
     * 重置密码
     */
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public AjaxResult resetPwd(@RequestBody UserEditDto sysUserEditDto) {
        userService.checkUserAllowed(sysUserEditDto.getUserId());
        userService.checkUserDataScope(sysUserEditDto.getUserId());
        SysUser sysUser = userService.getById(sysUserEditDto.getUserId());
        sysUser.setPassword(SecurityUtils.encryptPassword(sysUserEditDto.getPassword()));
        return toAjax(userService.updateById(sysUser));
    }

    /**
     * 状态修改
     */
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody UserEditDto sysUserEditDto) {
        userService.checkUserAllowed(sysUserEditDto.getUserId());
        userService.checkUserDataScope(sysUserEditDto.getUserId());
        SysUser sysUser = userService.getById(sysUserEditDto.getUserId());
        sysUser.setStatus(sysUserEditDto.getStatus());
        return toAjax(userService.updateById(sysUser));
    }

    /**
     * 根据用户编号获取授权角色
     */
    @RequiresPermissions("system:user:query")
    @GetMapping("/authRole/{userId}")
    public AjaxResult authRole(@PathVariable("userId") Long userId) {
        AjaxResult ajax = AjaxResult.success();
        SysUser user = userService.selectUserById(userId);
        List<SysRoleVo> roles = roleService.selectRolesByUserId(userId);
        ajax.put("user", user);
        ajax.put("roles", SecurityUtils.isAdmin(userId) ? roles : roles.stream().filter(r -> !SecurityUtils.isAdmin(r.getRoleId())).collect(Collectors.toList()));
        return ajax;
    }

    /**
     * 用户授权角色
     */
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authRole")
    public AjaxResult insertAuthRole(Long userId, Long[] roleIds) {
        userService.checkUserDataScope(userId);
        userService.insertUserAuth(userId, roleIds);
        return success();
    }

    /**
     * 获取部门树列表(有权限)
     */
    @GetMapping("/deptTree")
    public AjaxResult deptTree(DeptQuery query) {
        return success(deptService.selectAuthDeptTreeList(query));
    }

    /**
     * 获取部门树列表(无权限)
     */
    @GetMapping("/deptTreeNotAuth")
    public AjaxResult deptTreeNotAuth(DeptQuery query) {
        return success(deptService.selectDeptTreeList(query));
    }

    /**
     * 获取部门及他的子部门树
     */
    @GetMapping("/deptTreeChild")
    public AjaxResult deptTreeChild(DeptQuery query) {
        return success(deptService.selectDeptTreeChild(query));
    }
    /**
     * 获取部门列表
     */
    @GetMapping("/deptList")
    public AjaxResult deptList(DeptQuery query) {
        query.setStatus("0");
        List<SysDept> deptList = deptService.selectDeptList(query);
        Map<String, List<SysDept>> collect = deptList.stream().collect(Collectors.groupingBy(SysDept::getDeptLevel));
        return success(collect);
    }

    /**
     * 获取用户列表   (不走权限)
     */
    @InnerAuth
    @PostMapping("/getUserList")
    public R<List<SysUserVo>> userList(@RequestBody UserQuery query) {
        return R.ok(userService.selectUserListSpecial(null, query));
    }

    /**
     * 用户列表 - 公开的接口
     */
    @GetMapping("/publicUserList")
    public AjaxResult publicUserList(PublicUserQuery query) {
        return success(userService.getPublicUserList(query));
    }


    /**
     * 用户列表分页
     */
    @GetMapping("/publicPageUserList")
    public AjaxResult publicPageUserList(Page<PublicUserVo> page, PublicUserQuery query) {
        return success(page.setRecords(userService.getPublicPageUserList(page,query)));
    }

    /**
     * 获取用户列表   (走权限)
     */
    @InnerAuth
    @PostMapping("/getList")
    public R<List<SysUserVo>> list(@RequestBody UserQuery query) {
        return R.ok(userService.selectUserList(null, query));
    }

    /**
     * 查询分页用户列表   (走权限)
     */
    @InnerAuth
    @PostMapping("/getPageUserList")
    public R<Page<SysUserVo>> getPageUserList(@RequestBody QueryPage queryPage) {
        Page<SysUserVo> page = new Page<SysUserVo>().setCurrent(queryPage.getPage().getCurrent()).setSize(queryPage.getPage().getSize());
      //  return R.ok(page.setRecords(userService.selectUserList(page, UserQuery.builder().build())));
        return R.ok(page.setRecords(userService.selectUserList(page, queryPage.getUserQuery())));
    }

    /**
     * 查询分页用户列表   (走权限)
     */
    @InnerAuth
    @GetMapping("/getUserById")
    public R<SysUserVo> getUserById(Long userId) {
        return R.ok(userService.selectUserById(userId));
    }

    /**
     * 通过pdid找到用户id
     */
    @InnerAuth
    @GetMapping("/getUserByPdId")
    public R<SysUserPd> getUserByPdId(Long pdId) {
        return R.ok(userPdService.getOne(Wrappers.<SysUserPd>lambdaQuery()
                .eq(SysUserPd::getPdUserId, pdId).last("limit 1")));
    }



    /**
     * 查询分页用户列表   (走权限)
     */
    @GetMapping("/publicUserInfoById")
    public R<SysUserVo> publicUserInfoById(Long userId) {
        return R.ok(userService.selectUserById(userId));
    }

    /**
     * 查询用户通知列表 - 分页
     */
    @GetMapping("/notice/list")
    public AjaxResult noticeList(String status,String noticeType) {
        return success(userService.getMyNoticeList(status,noticeType));
    }

    /**
     * 查询用户未读通知数量
     */
    @GetMapping("/notice/unreadNumber")
    public AjaxResult noticeUnreadNumber() {
        return success(userService.getMyUnreadNoticeNumber());
    }

    /**
     * 查询PD账号信息
     */
    @Valid
    @GetMapping("/pd-info/{pdId}")
    public AjaxResult pdList(@PathVariable Long pdId, @NotBlank(message = "绑定类型不能为空") String type) {
        R<CmfAgentAdmin> r = null;
        // 推广
        if (DeptConstants.DEPT_TYPE_EX.equals(type)) {
            r = remoteCmfAdminService.getExtendById(pdId, SecurityConstants.INNER);
        }
        // 运营
        else if (DeptConstants.DEPT_TYPE_OP.equals(type)) {
            r = remoteCmfAdminService.getOperateById(pdId, SecurityConstants.INNER);
        }
        // VIP
        else if (DeptConstants.DEPT_TYPE_VIP.equals(type)) {
            r = remoteCmfAdminService.getServeById(pdId, SecurityConstants.INNER);
        }

        if (ObjUtil.isEmpty(r) || r.getCode() != R.SUCCESS || ObjUtil.isEmpty(r.getData())) {
            return warn("[" + pdId + "]不存在");
        }

        UserPdVo userPdVo = new UserPdVo();
        userPdVo.setPdUserId(pdId);
        userPdVo.setType(type);
        userPdVo.setPdNickName(r.getData().getNickname());
        return success(userPdVo);
    }

    /**
     * 获取部门树列表(无权限)
     */
    @GetMapping("/deptTreeAndUserInfo")
    public AjaxResult queryDeptAndUserInfo() {
        return success(deptService.getDeptTreeAndUserVo());
    }
}
