package com.yooa.system.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.constant.DictConstants;
import com.yooa.common.core.domain.R;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.common.log.annotation.Log;
import com.yooa.common.log.enums.BusinessType;
import com.yooa.common.security.annotation.InnerAuth;
import com.yooa.common.security.annotation.RequiresPermissions;
import com.yooa.system.api.domain.SysDept;
import com.yooa.system.api.domain.dto.DeptEditDto;
import com.yooa.system.api.domain.dto.DeptSaveDto;
import com.yooa.system.api.domain.dto.QueryPage;
import com.yooa.system.api.domain.query.DeptQuery;
import com.yooa.system.api.domain.vo.GroupDeptVo;
import com.yooa.system.service.DeptService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 部门管理 - 控制层
 */
@AllArgsConstructor
@RestController
@RequestMapping("/dept")
public class DeptController extends BaseController {
    private final DeptService deptService;

    /**
     * 获取部门列表
     */
    @RequiresPermissions("system:dept:list")
    @GetMapping("/list")
    public AjaxResult list(DeptQuery query) {
        List<SysDept> depts = deptService.selectDeptList(query);
        return success(depts);
    }

    /**
     * 查询部门列表（排除节点）
     */
    @RequiresPermissions("system:dept:list")
    @GetMapping("/list/exclude/{deptId}")
    public AjaxResult excludeChild(@PathVariable(value = "deptId", required = false) Long deptId) {
        List<SysDept> depts = deptService.selectDeptList(new DeptQuery());
        depts.removeIf(d -> d.getDeptId().intValue() == deptId || CollUtil.contains(StrUtil.split(d.getAncestors(), ","), deptId + ""));
        return success(depts);
    }

    /**
     * 根据部门编号获取详细信息
     */
    @RequiresPermissions("system:dept:query")
    @GetMapping(value = "/{deptId}")
    public AjaxResult getInfo(@PathVariable Long deptId) {
        deptService.checkDeptDataScope(deptId);
        return success(deptService.getDeptById(deptId));
    }

    /**
     * 新增部门
     */
    @RequiresPermissions("system:dept:add")
    @Log(title = "部门管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody DeptSaveDto dept) {
        if (!deptService.checkDeptNameUnique(dept)) {
            return error("新增部门'" + dept.getDeptName() + "'失败，部门名称已存在");
        }
        return toAjax(deptService.insertDept(dept));
    }

    /**
     * 修改部门
     */
    @RequiresPermissions("system:dept:edit")
    @Log(title = "部门管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody DeptEditDto dept) {
        Long deptId = dept.getDeptId();
        deptService.checkDeptDataScope(deptId);
        if (!deptService.checkDeptNameUnique(dept)) {
            return error("修改部门'" + dept.getDeptName() + "'失败，部门名称已存在");
        }
        else if (dept.getParentId().equals(deptId)) {
            return error("修改部门'" + dept.getDeptName() + "'失败，上级部门不能是自己");
        }
        else if (StrUtil.equals(DictConstants.SYS_DISABLE_YES, dept.getStatus()) && deptService.selectNormalChildrenDeptById(deptId) > 0) {
            return error("该部门包含未停用的子部门！");
        }
        return toAjax(deptService.updateDept(dept));
    }

    /**
     * 删除部门
     */
    @RequiresPermissions("system:dept:remove")
    @Log(title = "部门管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deptId}")
    public AjaxResult remove(@PathVariable Long deptId) {
        if (deptService.hasChildByDeptId(deptId)) {
            return warn("存在下级部门,不允许删除");
        }
        if (deptService.checkDeptExistUser(deptId)) {
            return warn("部门存在用户,不允许删除");
        }
        deptService.checkDeptDataScope(deptId);
        return toAjax(deptService.removeById(deptId));
    }

    /**
     * 根据部门条件查询部门信息详情(feign调用)
     */
    @InnerAuth
    @PostMapping("/getDeptList")
    public R<List<SysDept>> getDeptList(@RequestBody DeptQuery query) {
        return deptService.getDeptList(query);
    }

    @InnerAuth
    @PostMapping("/getPageDeptList")
    public R<Page<SysDept>> getPageDeptList(@RequestBody QueryPage queryPage) {
        Page<SysDept> page = new Page<SysDept>().setCurrent(queryPage.getPage().getCurrent()).setSize(queryPage.getPage().getSize());
        return R.ok(page.setRecords(deptService.getDeptList(page, queryPage.getDeptQuery())));
    }


}
